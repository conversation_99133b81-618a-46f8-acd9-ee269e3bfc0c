import { COUNTRY_CODE, CURRENCIES } from '../modules/country/country.schema';
import { PLAN_TYPE } from '../enums/plan.enum';
import { PAYMENT_METHODS } from '../enums/payment.enum';
import { ONBOARDING_STEPS_WITH_REWARDS } from '../modules/user/user.schema';
import { ReferralBonusConfig } from '../modules/user/referrals/referrals.schema';

export const ONE_MINUTE = 60 * 1000;
export const ONE_MINUTE_IN_SECONDS = 60;
export const ONE_HOUR = 60 * ONE_MINUTE;

const STORE_PUBLIC_HIDDEN_INFO =
  '-invites -owners -onboarding_steps -kyc -wallet -total_visits -subscription -milestones -security_pin -access_tokens -public_access_tokens -meta -wallets -disabled_slugs';

export const MAX_REFERRALS_EARNING_MONTHS = 3;
const CREDITS = {
  REFERRAL: {
    COMMISION: {
      SIGNUP: {
        NG: 500_00,
        GH: 10_00,
        KE: 50_00,
        ZA: 10_00,
      },
      SUBSCRIPTION: {
        NG: 1000_00,
        GH: 10_00,
        KE: 50_00,
        ZA: 10_00,
      },
      CASHOUT: {
        //min_cashout
        NG: 5000_00,
        GH: 50_00,
        KE: 500_00,
        ZA: 50_00,
      },
    },
  },
};

export const DEFAULT_BONUS_CONFIG = (country: string): ReferralBonusConfig => ({
  referrer: {
    type: 'fixed',
    amount: CREDITS.REFERRAL.COMMISION.SUBSCRIPTION[country] as number,
  },
  invitee: {
    type: 'fixed',
    amount: CREDITS.REFERRAL.COMMISION.SIGNUP[country] as number,
  },
  type: 'default',
});

export const ONBOARDING_CREDITS = {
  [ONBOARDING_STEPS_WITH_REWARDS.WATCH_SETUP_VIDEO]: {
    [CURRENCIES.NGN]: 500_00,
    [CURRENCIES.GHC]: 5_00,
    [CURRENCIES.KES]: 60_00,
    [CURRENCIES.ZAR]: 10_00,
  },
  [ONBOARDING_STEPS_WITH_REWARDS.ENABLE_PUSH_NOTIFICATION]: {
    [CURRENCIES.NGN]: 250_00,
    [CURRENCIES.GHC]: 3_00,
    [CURRENCIES.KES]: 30_00,
    [CURRENCIES.ZAR]: 5_00,
  },
  [ONBOARDING_STEPS_WITH_REWARDS.FIRST_ORDER_WITH_PAYMENT]: {
    [CURRENCIES.NGN]: 250_00,
    [CURRENCIES.GHC]: 3_00,
    [CURRENCIES.KES]: 30_00,
    [CURRENCIES.ZAR]: 5_00,
  },
  [ONBOARDING_STEPS_WITH_REWARDS.COMPLETE_KYC]: {
    [CURRENCIES.NGN]: 500_00,
    [CURRENCIES.GHC]: 5_00,
    [CURRENCIES.KES]: 60_00,
    [CURRENCIES.ZAR]: 10_00,
  },
  [ONBOARDING_STEPS_WITH_REWARDS.UPLOAD_10_PRODUCTS]: {
    [CURRENCIES.NGN]: 250_00,
    [CURRENCIES.GHC]: 3_00,
    [CURRENCIES.KES]: 30_00,
    [CURRENCIES.ZAR]: 5_00,
  },
};

const COUNTRY_CODE_MAP = {
  [COUNTRY_CODE.GH]: '+233',
  [COUNTRY_CODE.NG]: '+234',
  [COUNTRY_CODE.KE]: '+254',
  [COUNTRY_CODE.ZA]: '+27',
};

const COUNTRY_CODE_COUNTRY_MAP = {
  '+233': [COUNTRY_CODE.GH],
  '+234': [COUNTRY_CODE.NG],
  '+254': [COUNTRY_CODE.KE],
  '+27': [COUNTRY_CODE.ZA],
};

const COUNTRY_CODE_NAME_MAP = {
  [COUNTRY_CODE.GH]: {
    name: 'Ghana',
    demonym: 'Ghanian',
  },
  [COUNTRY_CODE.NG]: {
    name: 'Nigeria',
    demonym: 'Nigerian',
  },
  [COUNTRY_CODE.ZA]: {
    name: 'South Africa',
    demonym: 'South African',
  },
  [COUNTRY_CODE.KE]: {
    name: 'Kenya',
    demonym: 'Kenyan',
  },
};

const COUNTRY_PHONE_LENGTHS = {
  [COUNTRY_CODE.NG]: 10,
  [COUNTRY_CODE.GH]: 9,
  [COUNTRY_CODE.KE]: 9,
  [COUNTRY_CODE.ZA]: 9,
};

function getCountryFromPhone(phone: string): COUNTRY_CODE {
  const phoneCode = phone.split('-')[0];
  return COUNTRY_CODE_COUNTRY_MAP[phoneCode]?.[0] ?? COUNTRY_CODE.NG;
}

export const PAYMENT_METHODS_CURRENCY_MAP = {
  [CURRENCIES.NGN]: [PAYMENT_METHODS.TRANSFER, PAYMENT_METHODS.PAYSTACK, PAYMENT_METHODS.MONO_DIRECT_PAY],
  [CURRENCIES.GHC]: [PAYMENT_METHODS.MOMO, PAYMENT_METHODS.PAYSTACK],
  [CURRENCIES.KES]: [PAYMENT_METHODS.STARTBUTTON],
  [CURRENCIES.ZAR]: [PAYMENT_METHODS.STARTBUTTON],
  [CURRENCIES.USD]: [PAYMENT_METHODS.STRIPE],
  [CURRENCIES.GBP]: [PAYMENT_METHODS.LEATHERBACK],
  [CURRENCIES.CAD]: [PAYMENT_METHODS.LEATHERBACK],
};

const PAYMENTS_CREDIT_PERCENTAGE = 0.005;

const TEST_PAYMENT_AMOUNTS = {
  [CURRENCIES.NGN]: 100,
  [CURRENCIES.GHC]: 5,
  [CURRENCIES.KES]: 10,
  [CURRENCIES.ZAR]: 5,
};

const DELIVERIES_MARKUP = {
  [CURRENCIES.NGN]: 150,
  [CURRENCIES.GHC]: 2,
  [CURRENCIES.KES]: 20,
  [CURRENCIES.ZAR]: 2,
};

const TEST_PAYMENT_METHODS = {
  [CURRENCIES.NGN]: PAYMENT_METHODS.DIRECT_TRANSFER,
  [CURRENCIES.GHC]: PAYMENT_METHODS.PAYSTACK,
  [CURRENCIES.KES]: PAYMENT_METHODS.STARTBUTTON,
  [CURRENCIES.ZAR]: PAYMENT_METHODS.STARTBUTTON,
};

const DELIVERIES_CANCELLATION_FEE = {
  [CURRENCIES.NGN]: 200,
  [CURRENCIES.GHC]: 2,
};

const COLORS = {
  PRIMARY: '#332089',
  SECONDARY: '#EF940F',
};

export {
  STORE_PUBLIC_HIDDEN_INFO,
  CREDITS,
  PAYMENTS_CREDIT_PERCENTAGE,
  COUNTRY_CODE_MAP,
  TEST_PAYMENT_AMOUNTS,
  DELIVERIES_MARKUP,
  COUNTRY_CODE_COUNTRY_MAP,
  DELIVERIES_CANCELLATION_FEE,
  COLORS,
  getCountryFromPhone,
  COUNTRY_CODE_NAME_MAP,
  COUNTRY_PHONE_LENGTHS,
  TEST_PAYMENT_METHODS,
};

export const PLAN_FEATURES = {
  [PLAN_TYPE.STARTER]: [
    {
      title: 'Includes:',
      features: [
        'One Store',
        'Store Link',
        'Upload up to 10 Products',
        'Manage Orders & Customers',
        'Manage Discounts',
        'Search Engine Optimization',
        'Payments and Invoices',
        'Bank Account (NG)',
        'Customizable Store Link',
        'SMS Marketing [soon]',
      ],
    },
  ],
  [PLAN_TYPE.BASIC]: [
    {
      title: 'Includes:',
      features: [
        'Professional Online Store',
        'Search Engine Optimization',
        'Maximum of One Store',
        'Store Customizations',
        'Cart Tracking',
        'Import Products from Instagram',
        'Upload up to 100 Products',
        'Manage Product Options',
        'AI Product Recommendations',
        'Manage Orders & Customers',
        'Manage Discounts & Coupons',
        'Local Payments (Card, Momo, Bank Transfer, etc)',
        'Free Business Bank Account (NG)',
        'Paymemt Links, Invoices & Receipts',
        'Book Deliveries (NG & GH)',
        'Customer Testimonials',
        'Basic Bookkeeping [soon]',
      ],
    },
  ],
  [PLAN_TYPE.BUSINESS_PLUS]: [
    {
      title: 'Includes:',
      features: [
        'Everything in Basic',
        'Unlimited Stores in one Account',
        'Upload Unlimited Products',
        'Product Videos',
        'Product Highlights',
        'Product Info Blocks',
        'Facebook Pixel Tracking',
        'Custom Domain Names',
        'Export & Import Products',
        'Export Orders',
        'Global Payments (USD, GBP, +5 Currencies)',
        'Multiple Store Managers / Staff',
        'Affiliates',
        'Advanced Bookkeeping & Analytics [soon]',
      ],
    },
  ],
  [PLAN_TYPE.KITCHEN]: [
    {
      title: 'Chowbot',
      features: [
        'AI assisted ordering on Whatsapp',
        'Customer delivery info collection',
        'Automated payment collections',
        'Automated recording of orders',
        'Automated recording of customers',
      ],
    },
    {
      title: 'Business Tools',
      features: [
        'Storefront',
        'Inventory Management',
        'Business Analytics',
        'Record Keeping',
        'Simplified payment collection',
      ],
    },
    {
      title: 'Chowdeck',
      features: [
        'Request Deliveries with Chowdeck',
        'Import products from Chowdeck',
        'Sync product availability',
        'Chowdeck Orders Syncing',
      ],
    },
  ],
};

export const ACCEPTABLE_TOKEN_AMOUNTS = [50, 100, 250, 300, 400, 500, 1000, 1500, 2000, 2500];
export const AMOUNT_PER_TOKEN = 42;

export const DEFAULT_ITEM_IMAGE =
  'https://res.cloudinary.com/catlog/image/upload/v1713111825/new-placeholder-images/rice.png';

export const WALLET_LIMITS = {
  BEFORE_KYC: {
    [CURRENCIES.NGN]: {
      collection_limit: 1_000_000_00,
      daily_withdrawal_limit: 0,
    },
    [CURRENCIES.GHC]: {
      collection_limit: 10_000_00,
      daily_withdrawal_limit: 0,
    },
    [CURRENCIES.ZAR]: {
      collection_limit: 10_000_00,
      daily_withdrawal_limit: 0,
    },
    [CURRENCIES.KES]: {
      collection_limit: 1_00_000_00,
      daily_withdrawal_limit: 0,
    },
  },
  AFTER_KYC: {
    [CURRENCIES.NGN]: {
      collection_limit: 1_000_000_000_000_00,
      daily_withdrawal_limit: 1_000_000_00,
    },
    [CURRENCIES.GHC]: {
      collection_limit: 1_000_000_000_00,
      daily_withdrawal_limit: 10_000_00,
    },
    [CURRENCIES.ZAR]: {
      collection_limit: 1_000_000_000_00,
      daily_withdrawal_limit: 10_000_00,
    },
    [CURRENCIES.KES]: {
      collection_limit: 1_00_000_000_000_00,
      daily_withdrawal_limit: 1_00_000_00,
    },
  },
};

export const PAYMENT_FEE_PROFILES = {
  [CURRENCIES.NGN]: {
    [PLAN_TYPE.STARTER]: {
      percentage: 1.2,
      cap: 700_00,
    },
    [PLAN_TYPE.BASIC]: {
      percentage: 1,
      cap: 500_00,
    },
    [PLAN_TYPE.BUSINESS_PLUS]: {
      percentage: 0.8,
      cap: 300_00,
    },
    [PLAN_TYPE.KITCHEN]: {
      percentage: 0.8,
      cap: 300_00,
    },
  },
  [CURRENCIES.GHC]: {
    [PLAN_TYPE.STARTER]: {
      percentage: 1.2,
      cap: 7_00,
    },
    [PLAN_TYPE.BASIC]: {
      percentage: 1,
      cap: 5_00,
    },
    [PLAN_TYPE.BUSINESS_PLUS]: {
      percentage: 0.8,
      cap: 3_00,
    },
    [PLAN_TYPE.KITCHEN]: {
      percentage: 0.8,
      cap: 3_00,
    },
  },
  [CURRENCIES.ZAR]: {
    [PLAN_TYPE.STARTER]: {
      percentage: 1.2,
      cap: 7_00,
    },
    [PLAN_TYPE.BASIC]: {
      percentage: 1,
      cap: 5_00,
    },
    [PLAN_TYPE.BUSINESS_PLUS]: {
      percentage: 0.8,
      cap: 3_00,
    },
    [PLAN_TYPE.KITCHEN]: {
      percentage: 0.8,
      cap: 3_00,
    },
  },
  [CURRENCIES.KES]: {
    [PLAN_TYPE.STARTER]: {
      percentage: 1.2,
      cap: 70_00,
    },
    [PLAN_TYPE.BASIC]: {
      percentage: 1,
      cap: 50_00,
    },
    [PLAN_TYPE.BUSINESS_PLUS]: {
      percentage: 0.8,
      cap: 30_00,
    },
    [PLAN_TYPE.KITCHEN]: {
      percentage: 0.8,
      cap: 30_00,
    },
  },
  [CURRENCIES.USD]: {
    [PLAN_TYPE.STARTER]: {
      percentage: 1.2,
      cap: 50_00,
    },
    [PLAN_TYPE.BASIC]: {
      percentage: 1,
      cap: 50_00,
    },
    [PLAN_TYPE.BUSINESS_PLUS]: {
      percentage: 0.8,
      cap: 50_00,
    },
    [PLAN_TYPE.KITCHEN]: {
      percentage: 0.8,
      cap: 50_00,
    },
  },
  [CURRENCIES.GBP]: {
    [PLAN_TYPE.STARTER]: {
      percentage: 1.2,
      cap: 50_00,
    },
    [PLAN_TYPE.BASIC]: {
      percentage: 1,
      cap: 50_00,
    },
    [PLAN_TYPE.BUSINESS_PLUS]: {
      percentage: 0.8,
      cap: 50_00,
    },
    [PLAN_TYPE.KITCHEN]: {
      percentage: 0.8,
      cap: 50_00,
    },
  },
  [CURRENCIES.CAD]: {
    [PLAN_TYPE.STARTER]: {
      percentage: 1.2,
      cap: 50_00,
    },
    [PLAN_TYPE.BASIC]: {
      percentage: 1,
      cap: 50_00,
    },
    [PLAN_TYPE.BUSINESS_PLUS]: {
      percentage: 0.8,
      cap: 50_00,
    },
    [PLAN_TYPE.KITCHEN]: {
      percentage: 0.8,
      cap: 50_00,
    },
  },
};

export const FREE_PAYMENT_THRESHOLD = 10;

export const currenciesWithResolveAccount = [CURRENCIES.NGN, CURRENCIES.GHC];

export const currencyPairsToSkip = new Set<string>([
  // 'NGN/GHS',
  // 'NGN/ZAR',
  'NGN/KES',
  // 'GHS/NGN',
  'GHS/KES',
  'GHS/ZAR',
  'KES/ZAR',
  'KES/GHS',
  // 'ZAR/NGN',
  'ZAR/KES',
  'ZAR/GHS',
]);

export const SHIPBUBBLE_DELIVERY_CATEGORIES = [
  {
    category_id: ********,
    category: 'Hot food',
  },
  {
    category_id: ********,
    category: 'Dry food and supplements',
  },
  {
    category_id: ********,
    category: 'Electronics and gadgets',
  },
  {
    category_id: 2178251,
    category: 'Groceries',
  },
  {
    category_id: ********,
    category: 'Sensitive items (ATM cards, documents)',
  },
  {
    category_id: ********,
    category: 'Light weight items',
  },
  {
    category_id: ********,
    category: 'Machinery',
  },
  {
    category_id: ********,
    category: 'Medical supplies',
  },
  {
    category_id: ********,
    category: 'Health and beauty',
  },
  {
    category_id: ********,
    category: 'Furniture and fittings',
  },
  {
    category_id: 74794423,
    category: 'Fashion wears',
  },
];

export const DEFAULT_CURRENCY_MARKUP = 3;

export const OLD_UPFRONT_SUBSCRIPTION_AMOUNTS = {
  [PLAN_TYPE.BASIC]: {
    [CURRENCIES.NGN]: 2000_00,
    [CURRENCIES.GHC]: 25_00,
    [CURRENCIES.KES]: 250_00,
    [CURRENCIES.ZAR]: 49_00,
  },
  [PLAN_TYPE.BUSINESS_PLUS]: {
    [CURRENCIES.NGN]: 4250_00,
    [CURRENCIES.GHC]: 70_00,
    [CURRENCIES.KES]: 550_00,
    [CURRENCIES.ZAR]: 99_00,
  },
  [PLAN_TYPE.KITCHEN]: {
    [CURRENCIES.NGN]: 15000_00,
    [CURRENCIES.GHC]: 250_00,
    [CURRENCIES.KES]: 2500_00,
    [CURRENCIES.ZAR]: 499_00, //placholder
  },
};

export const UPFRONT_SUBSCRIPTION_AMOUNTS = {
  [PLAN_TYPE.BASIC]: {
    [CURRENCIES.NGN]: 3000_00,
    [CURRENCIES.GHC]: 35_00,
    [CURRENCIES.KES]: 300_00,
    [CURRENCIES.ZAR]: 60_00,
  },
  [PLAN_TYPE.BUSINESS_PLUS]: {
    [CURRENCIES.NGN]: 6500_00,
    [CURRENCIES.GHC]: 75_00,
    [CURRENCIES.KES]: 600_00,
    [CURRENCIES.ZAR]: 120_00,
  },
  [PLAN_TYPE.KITCHEN]: {
    [CURRENCIES.NGN]: 15000_00,
    [CURRENCIES.GHC]: 250_00,
    [CURRENCIES.KES]: 2500_00,
    [CURRENCIES.ZAR]: 499_00, //placholder
  },
};

export const DOMAIN_PURCHASE_MARKUPS = {
  [CURRENCIES.NGN]: 2000_00,
  [CURRENCIES.GHC]: 25_00,
  [CURRENCIES.KES]: 220_00,
  [CURRENCIES.ZAR]: 30_00,
};

export const CURRENT_EXCHANGE_RATES = {
  [CURRENCIES.NGN]: 1,
  [CURRENCIES.GHC]: 145,
  [CURRENCIES.KES]: 12,
  [CURRENCIES.ZAR]: 81,
};
