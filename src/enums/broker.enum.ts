export class BROKER_PATTERNS {
  static USER = {
    GET_USER: 'USER.GET_USER',
    GET_USERS: 'USER.GET_USERS',
    CREATE_USER: 'USER.CREATE_USER',
    AGGREGATE_USERS: 'USER.AGGREGATE_USERS',
    UPDATE_USER: 'USER.UPDATE_USER',
    ADD_STORE: 'USER.ADD_STORE',
    REDEEM_REFERRAL_CREDITS: 'USER.REDEEM_REFERRAL_CREDITS',
    REFERRAL_SUBSCRIBED: 'USER.REFERRAL_SUBSCRIBED',
    REFERRAL_WRAP_DATA: 'USER.REFERRAL_WRAP_DATA',
    GET_REFERRALS: 'USER.GET_REFERRALS',
    REMOVE_STORE: 'USER.REMOVE_STORE',
    VERIFY_JWT_TOKEN: 'USER.VERIFY_USER_TOKEN',
    GENERATE_JWT_TOKEN: 'USER.GENERATE_JWT_TOKEN',
    SEND_PUSH_NOTIFICATION: 'USER.SEND_PUSH_NOTIFICATION',
    VERIFY_PASSWORD: 'USER.VERIFY_PASSWORD',
    CUSTOMER_IO_STATUS: 'USER.CUSTOMER_IO_STATUS',
    MARK_USER_AS_QUALIFIED: 'USER.MARK_USER_AS_QUALIFIED',
    SEND_CONVERSION_EVENT_TO_META: 'USER.SEND_CONVERSION_EVENT_TO_META',

    CREDITS: {
      ADD_CREDITS: 'USER.CREDITS.ADD_CREDITS',
      GET_CREDITS: 'USER.CREDITS.GET_CREDITS',
      GET_CREDITS_FROM_STORE: 'USER.CREDITS.GET_CREDITS_FROM_STORE',
      DEBIT_CREDITS: 'USER.CREDITS.DEBIT_CREDITS',
      REVERSE_DEBIT: 'USER.CREDITS.REVERSE_DEBIT',
      RECORD_DEBIT: 'USER.CREDITS.RECORD_DEBIT',
      CHARGE_CREDITS_WALLET: 'USER.CREDITS.CHARGE_CREDITS_WALLET',
      CREDITS_EARNED_WRAP_DATA: 'USER.CREDITS.CREDITS_EARNED_WRAP_DATA',
      ADD_CREDITS_FOR_ONBOARDING_STEPS: 'USER.CREDITS.ADD_CREDITS_FOR_ONBOARDING_STEPS',
    },
  };

  static STORE = {
    CREATE_STORE_V2: 'STORE.CREATE_STORE_V2',
    GET_STORE: 'STORE.GET_STORE',
    GET_FIRST_STORE: 'STORE.GET_FIRST_STORE',
    GET_STORE_LEAN: 'STORE.GET_STORE_LEAN',
    PAGINATE_STORES_LEAN: 'STORE.PAGINATE_STORES_LEAN',
    GET_TOTAL: 'STORE.GET_TOTAL',
    ADD_VIEW: 'STORE.INCREMENT_COUNT',
    GET_STORES_LEAN: 'STORE.GET_STORES_LEAN',
    AGGREGATE_STORE: 'STORE.AGGREGATE_STORE',
    ADD_CATEGORIES: 'STORE.ADD_CATEGORIES',
    DISABLE_STORE_FEATURES: 'STORE.DISABLE_SLUG_AND_STORES',
    UPDATE_STORE: 'STORE.UPDATE_STORE',
    ADD_STORE_SUBSCRIPTION: 'STORE.ADD_STORE_SUBSCRIPTION',
    ENABLE_STORE_FEATURES: 'STORE.ENABLE_SLUG_AND_STORES',
    GET_DELIVERY_AREA: 'STORE.GET_DELIVERY_AREA',
    GET_DELIVERY_AREAS: 'STORE.GET_DELIVERY_AREAS',
    GET_STORE_DELIVERY_AREAS: 'STORE.GET_STORE_DELIVERY_AREAS',
    GET_CATEGORY_BY_ID: 'STORE.GET_CATEGORY_BY_ID',
    COUNT_DELIVERY_AREAS: 'STORE.COUNT_DELIVERY_AREAS',
    COUNT_STORES: 'STORE.COUNT_STORES',
    UPDATE_ITEMS_COUNT: 'STORE.UPDATE_ITEMS_COUNT',
    COUNT_SEO_STORES: 'STORE.COUNT_SEO_STORES',
    GET_SITEMAP_PAGE: 'STORE.GET_SITEMAP_PAGE',
    VERIFY_SECURITY_PIN: 'STORE.VERIFY_SECURITY_PIN',
    GET_BRANCHES: 'STORE.GET_BRANCHES',
    PAID_SUBSCRIPTION_STATUS: 'PAID_SUBSCRIPTION_STATUS',
    VALIDATE_STORE_OWNERSHIP: 'VALIDATE_STORE_OWNERSHIP',
    GET_YEAR_WRAP: 'STORE.GET_YEAR_WRAP',
    UPDATE_FIRST_ORDER_WITH_PAYMENT: 'STORE.UPDATE_FIRST_ORDER_WITH_PAYMENT',
    COMPLETE_DOMAIN_PURCHASE: 'STORE.COMPLETE_DOMAIN_PURCHASE',

    // CHOWDECK
    AUTO_SYNC_ITEM_TO_CHOWDECK: 'AUTO_SYNC_ITEM_TO_CHOWDECK',
    DISABLE_STORES_BY_IDS: 'store.disable_stores_by_ids',
    REMOVE_USER_FROM_STORE_OWNERS: 'store.remove_user_from_store_owners',
  };

  static ITEM = {
    GET_ITEM: 'ITEM.GET_ITEM',
    GET_SORTED_ITEMS: 'ITEM.GET_SORTED_ITEMS',
    GET_STORE_ITEMS_COUNT: 'ITEM.GET_STORE_ITEMS',
    GET_TOTAL: 'ITEM.GET_TOTAL',
    GET_SITEMAP_PAGE: 'ITEM.GET_SITEMAP_PAGE',
    ADD_VIEW: 'ITEM.INCREMENT_COUNT',
    ADD_TOTAL_ORDERS: 'ITEM.INCREMENT_TOTAL_ORDERS',
    GET_TOP_ITEMS: 'ITEM.GET_TOP_ITEMS',
    GET_ITEMS: 'ITEM.GET_ITEMS',
    GET_ITEMS_WITH_DISCOUNTS: 'ITEM.GET_ITEMS_WITH_DISCOUNTS',
    GET_COUPON: 'ITEM.GET_COUPON',
    VERIFY_COUPON: 'ITEM.VERIFY_COUPON',
    GET_DISCOUNTS: 'ITEM.GET_DISCOUNTS',
    TRACK_ORDER_COUPON: 'ITEM.TRACK_ORDER_COUPON',
    TRACK_ITEM_QUANTITIES: 'ITEM.TRACK_ITEM_QUANTITIES',
    CHECK_ITEM_QUANTITIES: 'CHECK_ITEM_QUANTITIES',
    UPDATE_ITEM_WEIGHTS: 'ITEM.UPDATE_ITEM_WEIGHTS',
    UPDATE_ITEMS: 'UPDATE_ITEMS',
    UPDATE_STORE_ITEMS: 'UPDATE_STORE_ITEMS',
    CREATE_ITEMS: 'CREATE_ITEMS',
    GET_MATCHING_ITEMS_WITH_NAME: 'GET_MATCHING_ITEMS_WITH_NAME',
    ADD_DEFAULT_QUANTITIES: 'ITEM.ADD_DEFAULT_QUANTITIES',
    UPDATE_INFO_BLOCKS: 'ITEM.UPDATE_INFO_BLOCKS',
    HIGHLIGHTS: {
      GET_HIGHLIGHT: 'ITEM.HIGHLIGHTS.GET_HIGHLIGHT',
      GET_HIGHLIGHTS: 'ITEM.HIGHLIGHTS.GET_HIGHLIGHTS',
      CREATE_HIGHLIGHT: 'ITEM.HIGHLIGHTS.CREATE_HIGHLIGHT',
      UPDATE_HIGHLIGHT: 'ITEM.HIGHLIGHTS.UPDATE_HIGHLIGHT',
      DELETE_HIGHLIGHT: 'ITEM.HIGHLIGHTS.DELETE_HIGHLIGHT',
      COUNT_HIGHLIGHTS: 'ITEM.HIGHLIGHTS.COUNT_HIGHLIGHTS',
    },
    GENERATE_STORE_ITEM_EMBEDDINGS: 'ITEM.GENERATE_STORE_ITEM_EMBEDDINGS',
    AGGREGATE_ITEMS_BY_TAGS: 'ITEM.AGGREGATE_ITEMS_BY_TAGS',
    CHECK_STOCK_THRESHOLD: 'ITEM.CHECK_STOCK_THRESHOLD',
    ENABLE_LOW_STOCK_TRACKING: 'ITEM.ENABLE_LOW_STOCK_TRACKING',
  };

  static PAYMENT = {
    GET_PAYMENT: 'PAYMENT.GET_PAYMENT',
    GET_TOTAL_PAYMENTS: 'PAYMENT.GET_TOTAL_PAYMENTS',
    GET_FIRST_PAYMENT: 'PAYMENT.GET_FIRST_PAYMENT',
    GET_ALL_PAYMENT_METHODS: 'PAYMENT.GET_ALL_PAYMENT_METHODS',
    CREATE_NEW_SUBSCRIPTION: 'PAYMENT.CREATE_NEW_SUBSCRIPTION',
    GET_SUBSCRIPTION: 'PAYMENT.GET_SUBSCRIPTION',
    GET_SUBSCRIPTION_IDS: 'PAYMENT.GET_SUBSCRIPTION_IDS',
    GET_MULTIPLE_SUBSCRIPTIONS: 'PAYMENT.GET_MULTIPLE_SUBSCRIPTIONS',
    CREATE_PAID_SUBSCRIPTION: 'PAYMENT.CREATE_PAID_SUBSCRIPTION',
    UPDATE_PAYMENT: 'PAYMENT.UPDATE_PAYMENT',
    UPDATE_SUBSCRIPTION: 'PAYMENT.UPDATE_SUBSCRIPTION',
    BASIC_SUBSCRIPTION_UPDATE: 'PAYMENT.BASIC_SUBSCRIPTION_UPDATE',
    UPDATE_BIGIN_USER: 'PAYMENT.UPDATE_BIGIN_USER',
    RECORD_TOKEN_PURCHASE: 'PAYMENT.RECORD_TOKEN_PURCHASE',
    EXTRA_TOKEN_PURCHASE: 'PAYMENT.EXTRA_TOKEN_PURCHASE',
    AGGREGATE_SUBSCRIPTION: 'PAYMENT.AGGREGATE_SUBSCRIPTION',
    AGGREGATE_SUBSCRIPTION_REMINDERS: 'PAYMENT.AGGREGATE_SUBSCRIPTION_REMINDERS',
    COUNT_SUBSCRIPTION_DOCUMENTS: 'PAYMENT.COUNT_SUBSCRIPTION_DOCUMENTS',
    COUNT_SUBSCRIPTION_PAYMENTS: 'PAYMENT.COUNT_SUBSCRIPTION_PAYMENTS',
    CREATE_PAYMENT_FOR_DOMAIN: 'PAYMENT.CREATE_PAYMENT_FOR_DOMAIN',
    WEBHOOKS: {
      RESOLVE_PAYSTACK_PAYMENTS: 'PAYMENT.WEBHOOKS.RESOLVE_PAYSTACK_PAYMENTS',
      PAYSTACK_STORE_PAYMENT: 'PAYMENT.WEBHOOKS.PAYSTACK_STORE_PAYMENT',
      PAYSTACK_SUBSCRIPTION: 'PAYMENT.WEBHOOKS.PAYSTACK_SUBSCRIPTION',
      PAYSTACK_WITHDRAWAL: 'PAYMENT.WEBHOOKS.PAYSTACK_TRANSFER',
      PAYSTACK_CHARGE: 'PAYMENT.WEBHOOKS.PAYSTACK_CHARGE',
      PAYSTACK_TRANSFER: 'PAYMENT.WEBHOOKS.PAYSTACK_TRANSFER',
      PAYSTACK_REVERSAL: 'PAYMENT.WEBHOOKS.PAYSTACK_REVERSAL',
      PAYSTACK_FAILED: 'PAYMENT.WEBHHOKS.PAYSTACK_FAILED',
      PAYSTACK_DD_AUTH_CREATED: 'PAYMENT.PAYSTACK_DD_AUTH_CREATED',
      SQUAD_TRANSFER_RECIEVED: 'PAYMENT.WEBHOOKS.SQUAD_TRANSFER_RECIEVED',
      BLOCHQ_WITHDRAWAL: 'PAYMENT.WEBHOKS.BLOCHQ_WITHDRAWAL',
      BLOCHQ_TRANSFER_RECEIVED: 'PAYMENT.WEBHOOKS.BLOCHQ_TRANSFER_RECIEVED',
      FLW_TRANSFER_RECIEVED: 'PAYMENT.WEBHOOKS.FLW_TRANSFER_RECIEVED',
      KORAPAY_TRANSFER_RECEIVED: 'PAYMENT.WEBHOOKS.KORAPAY_TRANSFER_RECEIVED',
      MONNIFY_TRANSFER_RECEIVED: 'PAYMENT.WEBHOOKS.MONNIFY_TRANSFER_RECEIVED',
      ZILLA_PAYMENT_CONFIRMED: 'PAYMENT.WEBHOOK.ZILLA_PAYMENT_CONFIRMED',
      MONO_PAYMENT_CONFIRMED: 'PAYMENT.WEBHOOK.MONO_PAYMENT_CONFIRMED',
      THEPEER_PAYMENT_RECEIVED: 'PAYMENT.WEBHOOK.THEPEER_PAYMENT_RECEIVED',
      RESOLVE_ZEEPAY_PAYMENT: 'PAYMENT.WEBHOOK.RESOLVE_ZEEPAY_PAYMENT',
      PAYAZA_PAYMENT_RECEIVED: 'PAYMENT.WEBHOOKS.PAYAZA_PAYMENT_RECEIVED',
      PAYAZA_TRANSFER_RECEIVED: 'PAYMENT.WEBHOOKS.PAYAZA_TRANSFER_RECEIVED',
      FINCRA_CONVERSION: 'PAYMENT.WEBHOOKS.FINCRA_CONVERSION',
      STARTBUTTON_PAYMENT_RECEIVED: 'PAYMENT.WEBHOOKS.STARTBUTTON_PAYMENT_RECEIVED',
      WITHDRAWAL_RESPONSE: 'PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE',
      STRIPE_PAYMENT_RECEIVED: 'PAYMENT.WEBHOOKS.STRIPE_PAYMENT_RECEIVED',
      LEATHERBACK_PAYMENT_RECEIVED: 'PAYMENT.WEBHOOKS.LEATHERBACK_PAYMENT_RECEIVED',
      SUDO_TRANSFER_RECEIVED: 'PAYMENT.WEBHOOKS.SUDO_TRANSFER_RECEIVED',
      SUDO_WITHDRAWAL: 'PAYMENT.WEBHOOKS.SUDO_WITHDRAWAL',
    },
    GET_SUBSCRIPTIONS_WITH_OWNER: 'PAYMENT.GET_SUBSCRIPTIONS_WITH_OWNER',
    GET_SUBSCRIPTIONS_LEAN: 'PAYMENT.GET_SUBSCRIPTIONS_LEAN',
    CREATE_PUBLIC_PAYMENT: 'PAYMENT.CREATE_PUBLIC_PAYMENT',
    MANUALLY_RECORD_PAYMENT: 'PAYMENT.MANUALLY_RECORD_PAYMENT',
    CREATE_TEST_PAYMENT: 'PAYMENT.CREATE_TEST_PAYMENT',
    TEST_PAYMENT_PAID: 'PAYMENT.TEST_PAYMENT_PAID',
    UPDATE_BIGIN_RECORD_ID: 'PAYMENT.UPDATE_BIGIN_RECORD_ID',

    TOKENS: {
      GET_TOKEN_BALANCE: 'PAYMENT.TOKENS.GET_TOKEN_BALANCE',
      DEBIT_TOKEN: 'PAYMENT.TOKENS.DEBIT_TOKEN',
    },
  };

  static PLAN = {
    GET_PLAN_BY_ID: 'PLAN_GET_PLAN_BY_ID',
    GET_PLAN: 'PLAN_GET_PLAN',
    GET_PLAN_OPTION: 'PLAN_GET_PLAN_OPTIONS',
    GET_PLANS: 'PLAN.GET_PLANS',
    GET_PLANS_LEAN: 'GET_PLANS_LEAN',
  };

  static MAIL = {
    SEND_RESET_PASSWORD_TOKEN: 'MAIL.RESET_PASSWORD',
    VERIFY_EMAIL: 'MAIL.VERIFY_EMAIL',
    VERIFY_WITHDRAWAL: 'MAIL.VERIFY_WITHDRAWAL',
    TRIAL_ENDED: 'MAIL.TRIAL_ENDED',
    WELCOME_EMAIL: 'MAIL.WELCOME_EMAIL',
    RENEW_SUBSCRIPTION: 'MAIL.RENEW_SUBSCRIPTION',
    NEW_SUBSCRIPTION: 'MAIL.NEW_SUBSCRIPTION',
    SUBSCRIPTION_SUCCESSFUL: 'MAIL.SUBSCRIPTION_SUCCESSFUL',
    SUBSCRIPTION_CANCELLED: 'MAIL.SUBSCRIPTION_CANCELLED',
    SUBSCRIPTION_REMINDER: 'MAIL.SUBSCRIPTION_REMINDER',
    ORDER_PROCESSING: 'MAIL.ORDER_PROCESSING',
    ORDER_FULFILLED: 'MAIL.ORDER_FULFILLED',
    ORDER_CANCELLED: 'MAIL.ORDER_CANCELLED',
    ORDER_RECEIVED_CUSTOMER: 'MAIL.ORDER_RECEIVED_CUSTOMER',
    ORDER_RECEIVED: 'MAIL.ORDER_RECEIVED',
    ORDER_STATUS: 'MAIL.ORDER_STATUS',
    SEND_PRODUCT_REMINDER: 'MAIL.SEND_PRODUCT_REMINDER',
    BVN_VERIFICATION: 'MAIL.BVN_VERIFICATION',
    ACCEPT_STORE_INVITE: 'MAIL.ACCEPT_STORE_INVITE',
    HUNDRED_STORE_VISITS: 'MAIL.HUNDRED_STORE_VISITS',
    FIVE_HUNDRED_STORE_VISITS: 'MAIL.FIVE_HUNDRED_STORE_VISITS',
    THOUSAND_STORE_VISITS: 'MAIL.THOUSAND_STORE_VISITS',
    FIVE_HUNDRED_THOUNSAND_SALES: 'MAIL.FIVE_HUNDRED_THOUNSAND_SALES',
    ONE_MILLION_SALES: 'MAIL.ONE_MILLION_SALES',
    DELETED_PRODUCT_IMAGES: 'MAIL.DELETED_PRODUCT_IMAGES',
    INVOICE_RECEIVED: 'MAIL.INVOICE_RECEIVED',
    WITHDRAWAL_SUCCESSFUL: 'MAIL.WITHDRAWAL_SUCCESSFUL',
    WITHDRAWAL_FAILED: 'MAIL.WITHDRAWAL_FAILED',
    FUNDS_REVERSED: 'MAIL.FUNDS_REVERSED',
    PAYMENT_RECEIVED: 'MAIL.PAYMENT_RECEIVED',
    PAYMENT_SUCCESSFUL: 'MAIL.PAYMENT_SUCCESSFUL',
    ORDER_SUCCESSFUL: 'MAIL.ORDER_SUCCESSFUL',
    PAYMENTS_ACTIVATED: 'MAIL.PAYMENTS_ACTIVATED',
    PAYMENTS_ACTIVATED_GH: 'MAIL.PAYMENTS_ACTIVATED_GH',
    VERIFY_WALLET_PAYMENT: 'MAIL.VERIFY_WALLET_PAYMENT',
    BUSINESS_ACCOUNT_CREATED: 'MAIL.BUSINESS_ACCOUNT_CREATED',
    TRANSFER_RECEIVED: 'MAIL.TRANSFER_RECEIVED',
    KYC_REJECTED: 'MAIL.KYC_REJECTED',
    USER_REFERRED: 'MAIL.USER_REFERRED',
    CREDITS_RECEIVED: 'MAIL.CREDITS_RECEIVED',
    CONVERSION_SUCCESSFUL: 'MAIL.CONVERSION_SUCCESSFUL',
    CONVERSION_FAILED: 'MAIL.CONVERSION_FAILED',
    WALLET_REQUEST_APPROVED: 'MAIL.WALLET_REQUEST_APPROVED',
    WALLET_REQUEST_REJECTED: 'MAIL.WALLET_REQUEST_REJECTED',

    //WEEKLY AND MONTHLY EMAIL SUMMARY
    WEEK_MONTHLY_SUMMARIES: 'MAIL.WEEK_MONTHLY_SUMMARIES',

    //MILESTONES
    STORE_VISITS_MILESTONE: 'MAIL.STORE_VISITS_MILESTONE',
    SALES_MILESTONE: 'MAIL.SALES_MILESTONE',
    ORDERS_MILESTONE: 'MAIL.ORDERS_MILESTONE',
    PAYMENTS_MILESTONE: 'MAIL.PAYMENTS_MILESTONE',

    //DELIVEREIS
    DELIVERY_BOOKED: 'MAIL.DELIVERY_BOOKED',
    SELLER_DELIVERY_STATUS: 'MAIL.SELLER_DELIVERY_STATUS',
    CUSTOMER_DELIVERY_STATUS: 'MAIL.CUSTOMER_DELIVERY_STATUS',

    //CHOWBOT
    PRODUCT_IMPORT_SUCCESSFUL: 'MAIL.PRODUCT_IMPORT_SUCCESSFUL',
    PRODUCT_EXPORT_SUCCESSFUL: 'MAIL.PRODUCT_EXPORT_SUCCESSFUL',
    SYNCED_CHOWDECK_ITEMS: 'MAIL.SYNCED_CHOWDECK_ITEMS',
    EXTRA_TOKEN_PURCHASE: 'MAIL.EXTRA_TOKEN_PURCHASE',
    TOKENS_THRESHOLD_NOTIFICATION: 'MAIL.TOKENS_THRESHOLD_NOTIFICATION',
    CHOWBOT_NEW_SUBSCRIPTION: 'MAIL.CHOWBOT_NEW_SUBSCRIPTION',
    CHOWBOT_SUBSCRIPTION_RENEWED: 'MAIL.CHOWBOT_SUBSCRIPTION_RENEWED',
    CHOWBOT_RENEWAL_NOTICE: 'MAIL.CHOWBOT_RENEWAL_NOTICE',
    CHOWBOT_SUBSCRIPTION_CANCELLED: 'MAIL.CHOWBOT_SUBSCRIPTION_CANCELLED',

    //AFFILIATES
    AFFILIATE_INVITE: 'MAIL.AFFILIATE_INVITE',
    AFFILIATE_ORDER: 'MAIL.AFFILIATE_ORDER',

    //DOMAIN PURCHASE
    DOMAIN_PURCHASE_SUCCESSFUL: 'MAIL.DOMAIN_PURCHASE_SUCCESSFUL',

    //ITEMS IMPORT
    PRODUCT_IMPORT_UPDATE_SUCCESSFUL: 'MAIL.PRODUCT_IMPORT_UPDATE_SUCCESSFUL',

    //STOCK THRESHOLD
    STOCK_THRESHOLD_NOTIFICATION: 'MAIL.STOCK_THRESHOLD_NOTIFICATION',
  } as const;

  static MESSAGING = {
    WHATSAPP: 'WHATSAPP.MESSAGE',
    SMS: 'SMS.MESSAGE',
    SEND_WHATSAPP: 'DECOUPLED',
  };

  static COUNTRY = {
    GET_COUNTRY: 'COUNTRY.GET_COUNTRY',
    GET_ALL_COUNTRIES: 'COUNTRY.GET_ALL_COUNTRIES',
  };

  static CART = {
    GET_TOTAL: 'CART_GET_TOTAL',
    GET_CART: 'GET_CART',
    GET_RESOLVED_CART: 'GET_RESOLVED_CART',
    CREATE_CART: 'CREATE_CART',
    UPDATE_CART: 'UPDATE_CART',
  };

  static ORDER = {
    GET_ORDER: 'ORDER.GET_ORDER',
    GET_ORDER_PROPERTIES: 'GET_ORDER_PROPERTIES',
    UPDATE_ORDER_STATUS: 'ORDER.UPDATE_ORDER',
    GET_ALL: 'ORDER.GET_ALL',
    GET_ALL_LEAN: 'ORDER.GET_ALL_LEAN',
    GET_CUSTOMER: 'ORDER.GET_CUSTOMER',
    GET_CUSTOMERS: 'ORDER.GET_CUSTOMERS',
    CREATE_CUSTOMER: 'ORDER.CREATE_CUSTOMER',
    UPDATE_CUSTOMER: 'ORDER.UPDATE_CUSTOMER',
    GET_TOTAL_ORDER_AMOUNT: 'STORE.GET_TOTAL_ORDER_AMOUNT',
    ADD_STATUS_TO_TIMELINE: 'ADD_STATUS_TO_TIMELINE',
    GET_ORDER_LEAN: 'GET_ORDER_LEAN',
    ADD_ORDER_RECEIPT: 'ADD_ORDER_RECEIPT',
    UPDATE_ORDER: 'UPDATE_ORDER',
    CREATE_ORDER: 'CREATE_ORDER',
    COUNT_ORDERS: 'COUNT_ORDERS',
    MARK_ORDER_AS_PAID: 'MARK_ORDER_AS_PAID',
    CONFIRM_ORDER: 'CONFIRM_ORDER',
    RECORD_FEEDBACK: 'ORDER.RECORD_FEEDBACK',
    GET_WRAP_DATA: 'ORDER.GET_WRAP_DATA',
    GET_MILESTONES: 'ORDER.GET_MILESTONES',
  };

  static ANALYTICS = {
    GET_STORE_VISITS: 'ANALYTICS.GET_STORE_VISITS',
    TOTAL_STORE_VISITS: 'ANALYTICS.TOTAL_STORE_VISITS',
    TOTAL_PAID_STORES: 'ANALYTICS.TOTAL_PAID_STORES',
    TOTAL_NUMBER_OF_STORES: 'ANALYTICS.TOTAL_NUMBER_OF_STORES',
    TOTAL_STORES_CREATED_TODAY: 'ANALYTICS.TOTAL_STORES_CREATED_TODAY',

    TOTAL_USERS: 'ANALYTICS.TOTAL_USERS',
  };

  static KYC = {
    GET_KYC: 'KYC.GET_KYC',
    UPDATE_KYC: 'KYC.UPDATE_KYC',
  };

  static BVN = {
    GET_BVN: 'BVN.GET_BVN',
  };

  static IP_ADDRESS = {
    RESOLVE_IP: 'IP_ADDRESS.RESOLVE_IP',
  };

  static WALLET = {
    CREATE_WALLET: 'WALLET.CREATE_WALLET',
    INITIATE_WALLET: 'WALLET.INITIATE_WALLET',
    CREDIT: 'WALLET.CREDIT',
    GET_SETUP_PROGRESS: 'WALLET.GET_SETUP_PROGRESS',
    GET_WALLET: 'WALLET.GET_WALLET',
    GET_ACCOUNTS: 'WALLET.GET_ACCOUNTS',
    GET_WALLETS: 'WALLET.GET_WALLETS',
    GET_TRANSACTIONS: 'WALLET.GET_TRANSACTIONS',
    GET_TRANSACTION: 'WALLET.GET_TRANSACTION',
    MIGRATE_SQUAD_PAYMENTS: 'WALLET.WEBHOOKS.MIGRATE_SQUAD_PAYMENTS',
    ADD_TRANSFER_FEES: 'WALLET.WEBHOOKS.ADD_TRANSFER_FEES',
    DEBIT_WALLET: 'WALLET.DEBIT_WALLET',
    REFUND_PAYMENT_TO_WALLET: 'WALLET.REFUND_PAYMENT_TO_WALLET',
    GET_EXCHANGE_RATES: 'WALLET.GET_EXCHANGE_RATES',
    GET_EXCHANGE_RATE: 'WALLET.GET_EXCHANGE_RATE',
    AUTO_SEND_FUNDS_TO_ACCOUNT: 'WALLET.AUTO_SEND_FUNDS_TO_ACCOUNT',
    COUNT_CREDIT_PAYMENTS: 'WALLET.COUNT_CREDIT_PAYMENTS',
    UPDATE_WALLET_CURRENCY: 'WALLET.UPDATE_WALLET_CURRENCY',
    GET_MILESTONES: 'WALLET.GET_MILESTONES',
    GET_CURRENCY_RATES: 'WALLET.GET_CURRENCY_RATES',
    GET_CURRENCY_RATES_FOR_CURRENCY: 'WALLET.GET_CURRENCY_RATES_FOR_CURRENCY',
    REVERSE_TRANSACTION: 'WALLET.REVERSE_TRANSACTION',
    CHARGE_CUSTOMER_TRANSFER_FEE: 'WALLET.CHARGE_CUSTOMER_TRANSFER_FEE',
  };

  static INVOICE = {
    GET_INVOICE: 'INVOICE.GET_INVOICE',
    GET_INVOICES: 'INVOICE.GET_INVOICES',
    FROM_ORDER: 'INVOICE.FROM_ORDER',
    UPDATE_FROM_ORDER: 'INVOICE.UPDATE_FROM_ORDER',
    GET_STATISTICS: 'INVOICE.GET_STATISTICS',
    INVOICE_PAID: 'INVOICE.INVOICE_PAID',
    ADD_RECEIPT_TO_INVOICE: 'INVOICE.ADD_RECEIPT_TO_INVOICE',
    UPDATE_TO_EXPIRED: 'INVOICE.UPDATE_TO_EXPIRED',
  };

  static WEBSOCKET = {
    TARGETTED_MESSAGE: 'WEBSOCKET.TARGETTED_MESSAGE',
  };

  static SSE = {
    TARGETTED_MESSAGE: 'SSE.TARGETTED_MESSAGE',
  };

  static URL_SHORTENER = {
    SHORTEN: 'URL_SHORTENER.SHORTEN',
  };

  static RECEIPT = {
    GENERATE_FROM_ORDER: 'RECEIPT.GENERATE_FROM_ORDER',
    GENERATE_FROM_INVOICE: 'RECEIPT.GENERATE_FROM_INVOICE',
    GET_RECEIPT_PDF_LINK: 'RECEIPT.GET_RECEIPT_PDF_LINK',
  };

  static AFFILIATE = {
    CREATE_AFFILIATE: 'AFFILIATE.CREATE_AFFILIATE',
    GET_AFFILIATE: 'AFFILIATE.GET_AFFILIATE',
    GET_AFFILIATES: 'AFFILIATE.GET_AFFILIATES',
    UPDATE_ORDERS: 'AFFILIATE.UPDATE_ORDERS',
    UPDATE_CUSTOMERS: 'AFFILIATE.UPDATE_CUSTOMERS',
  };

  static DELVERIES = {
    CREATE_AUTO_DELIVERY: 'CREATE_AUTO_DELIVERY',
    UPDATE_ADDRESS: 'UPDATE_ADDRESS',
    UPDATE_DELIVERY: 'UPDATE_DELIVERY',
    UPDATE_STATUS: 'UPDATE_STATUS',
    GET_DELIVERY: 'GET_DELIVERY',
    INITIATE_DELIVERY: 'INITIATE_DELIVERY',
    CREATE_ADDRESS: 'CREATE_ADDRESS',
    CREATE_ADDRESS_USING_GOOGLE_VALIDATION: 'CREATE_ADDRESS_USING_GOOGLE_VALIDATION',
    SAVE_ADDRESS: 'SAVE_ADDRESS',
    GET_ADDRESS: 'GET_ADDRESS',
    GET_DELIVERIES_WRAP_DATA: 'GET_DELIVERIES_WRAP_DATA',
  };

  static CONFIG = {
    GET_CONFIG: 'CONFIG.GET_CONFIG',
    GET_APP_CONFIGURATION: 'CONFIG.GET_APP_CONFIGURATION',
    UPDATE_MOBILE_APP_SIGNIN_COUNTER: 'CONFIG.UPDATE_MOBILE_APP_SIGNIN_COUNTER',
  };

  static WHATSAPP_BOT = {
    HANDLE_TEXT_MESSAGE: 'WHATSAPP_BOT.HANDLE_TEXT_MESSAGE',
    UPDATE_PAYMENT_ID: 'WHATSAPP_BOT.UPDATE_PAYMENT_ID',
    REFRESH_STORE_MENU: 'WHATSAPP_BOT.REFRESH_STORE_MENU',
    HANDLE_MESSAGE_STATUS: 'WHATSAPP_BOT.HANDLE_MESSAGE_STATUS',
    HANDLE_INTERACTIVE_MESSAGE: 'WHATSAPP_BOT.HANDLE_INTERACTIVE_MESSAGE',
    HANDLE_SUCESSFUL_PAYMENT: 'WHATSAPP_BOT.HANDLE_SUCESSFUL_PAYMENT',
    HANDLE_ORDER_STATUS_UPDATE: 'WHATSAPP_BOT.HANDLE_ORDER_STATUS_UPDATE',
    HANDLE_FAILED_AUTO_DELIVERY: 'WHATSAPP_BOT.HANDLE_FAILED_AUTO_DELIVERY',
    HANDLE_AUTO_DELIVERY_MERCHANT_STATUS_UPDATE: 'WHATSAPP_BOT.HANDLE_AUTO_DELIVERY_MERCHANT_STATUS_UPDATE',
    AUTO_DELIVERY_BOOKED: 'WHATSAPP_BOT.AUTO_DELIVERY_BOOKED',
    GET_TOKEN_USAGE: 'WHATSAPP_BOT.GET_TOKEN_USAGE',
    GET_TOKEN_AVERAGE_DAILY_USAGE: 'WHATSAPP_BOT.GET_TOKEN_AVERAGE_DAILY_USAGE',
    SEND_DELIVERY_STATUS: 'WHATSAPP_BOT.SEND_DELIVERY_STATUS',
  };

  static DOMAIN = {
    CHECK_AVAILABILITY: 'DOMAIN.CHECK_AVAILABILITY',
    INITIATE_PURCHASE: 'DOMAIN.INITIATE_PURCHASE',
    COMPLETE_PURCHASE: 'DOMAIN.COMPLETE_PURCHASE',
    GET_PURCHASES: 'DOMAIN.GET_PURCHASES',
    GET_PURCHASE: 'DOMAIN.GET_PURCHASE',
    CREATE_PAYMENT: 'DOMAIN.CREATE_PAYMENT',
    QUEUE_DNS_SETUP: 'DOMAIN.QUEUE_DNS_SETUP',
    CREATE_PURCHASE: 'DOMAIN.CREATE_PURCHASE',
    UPDATE_PURCHASE: 'DOMAIN.UPDATE_PURCHASE',
  };

  static SUBSCRIPTION = {
    CREATE_DISCOUNT: 'SUBSCRIPTION.CREATE_DISCOUNT',
    APPLY_DISCOUNT: 'SUBSCRIPTION.APPLY_DISCOUNT',
    CHECK_ACTIVE_DISCOUNT: 'SUBSCRIPTION.CHECK_ACTIVE_DISCOUNT',
    ISSUE_MOBILE_ACTIVE_LOGIN_DISCOUNT: 'SUBSCRIPTION.ISSUE_MOBILE_ACTIVE_LOGIN_DISCOUNT',
  };
}

export const BROKER_TRANSPORT = 'BROKER_TRANSPORT';
