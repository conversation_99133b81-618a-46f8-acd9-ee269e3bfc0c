import { Test, TestingModule } from '@nestjs/testing';
import { DirectServiceAccessService } from './direct-service-access.service';
import { ServicesRegistry } from '../service-registry';

describe('DirectServiceAccessService', () => {
  let service: DirectServiceAccessService;
  let registry: ServicesRegistry;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DirectServiceAccessService, ServicesRegistry],
    }).compile();

    service = module.get<DirectServiceAccessService>(DirectServiceAccessService);
    registry = module.get<ServicesRegistry>(ServicesRegistry);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(registry).toBeDefined();
  });

  it('should register and retrieve services', () => {
    // Mock service
    const mockService = {
      testMethod: jest.fn().mockReturnValue('test result'),
    };

    // Register the service
    service.register('TestService', mockService);

    // Retrieve the service
    const retrievedService = service.service<typeof mockService>('TestService');

    expect(retrievedService).toBe(mockService);
    expect(retrievedService.testMethod()).toBe('test result');
  });

  it('should provide type-safe convenience methods', () => {
    // Mock PlanService
    const mockPlanService = {
      getPlansLean: jest.fn().mockResolvedValue([
        { _id: '1', name: 'Basic Plan', type: 'BASIC' },
        { _id: '2', name: 'Pro Plan', type: 'PRO' },
      ]),
    };

    // Register PlanService
    service.register('PlanService', mockPlanService);

    // Access through convenience method
    const planService = service.plans();

    expect(planService).toBe(mockPlanService);
  });

  it('should handle missing services gracefully in test environment', () => {
    // Set test environment
    process.env.NODE_ENV = 'test';

    // Try to get a non-existent service
    const nonExistentService = service.service('NonExistentService');

    // Should return a mock service in test environment
    expect(nonExistentService).toBeDefined();
    expect(typeof nonExistentService).toBe('object');
  });
});
