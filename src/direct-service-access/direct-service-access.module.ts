import { Global, Module, OnModuleInit } from '@nestjs/common';
import { DirectServiceAccessService } from './direct-service-access.service';
import { ServicesRegistry } from '../service-registry';
import { ModuleRef } from '@nestjs/core';

/**
 * Module that provides direct access to services registered in the ServicesRegistry.
 * This is made @Global to ensure it's available throughout the application when imported through SharedModule.
 */
@Global()
@Module({
  imports: [],
  providers: [DirectServiceAccessService, ServicesRegistry],
  exports: [DirectServiceAccessService, ServicesRegistry],
})
export class DirectServiceAccessModule implements OnModuleInit {
  constructor(private moduleRef: ModuleRef, private servicesRegistry: ServicesRegistry) {}

  onModuleInit() {
    // Register service names that might be used before they're normally registered
    const serviceNames = [
      'UserService',
      'StoreService',
      'OrdersService',
      'ItemService',
      'PaymentService',
      'StoreUpdatesService',
      'StoreMigrationService',
      'StoreThirdPartyConnectionsService',
      // Add other services that are used early in the application lifecycle
    ];

    // Try to resolve and register each service
    for (const serviceName of serviceNames) {
      try {
        const service = this.moduleRef.get(serviceName, { strict: false });
        if (service) {
          this.servicesRegistry.register(serviceName, service);
        }
      } catch (e) {
        // If service can't be resolved yet, we'll skip and let it be registered normally later
      }
    }
  }
}
