import { Injectable } from '@nestjs/common';
import { ServicesRegistry } from '../service-registry';

// Import service types for proper TypeScript inference
import type { StoreService } from '../modules/store/services/store.service';
import type { CountryService } from '../modules/country/country.service';
import type { PlanService } from '../modules/plan/plan.service';
import type { OrdersService } from '../modules/orders/orders.service';
import type { UserService } from '../modules/user/user.service';
import type { ItemService } from '../modules/item/item.service';
import type { PaymentService } from '../modules/payment/services/index.service';
import type { PaymentGettersService } from '../modules/payment/services/payment.getters.service';
import type { WalletService } from '../modules/wallets/wallet.service';
import type { CartService } from '../modules/cart/cart.service';
import type { AnalyticService } from '../modules/analytic/analytic.service';
import type { InvoiceService } from '../modules/invoice/invoice.service';
import type { SubscriptionService } from '../modules/subscription/subscription.service';
import type { DeliveryService } from '../modules/deliveries/deliveries.service';
import type { CustomerService } from '../modules/orders/customers/customer.service';
import type { DeliveryAreaService } from '../modules/store/delivery-areas/delivery-areas.service';

/**
 * Centralized service that provides direct access to all registered services
 * This replaces the module-specific DirectServiceAccess classes with a single,
 * consistent service that can be used throughout the application
 */
@Injectable()
export class DirectServiceAccessService {
  constructor(public readonly servicesRegistry: ServicesRegistry) {}

  /**
   * Get direct access to the OrdersService with proper typing
   * @returns The OrdersService instance with its type
   */
  orders<T = OrdersService>(): T {
    return this.servicesRegistry.getService<T>('OrdersService');
  }

  /**
   * Get direct access to the StoreService with proper typing
   * @returns The StoreService instance with its type
   */
  stores<T = StoreService>(): T {
    return this.servicesRegistry.getService<T>('StoreService');
  }

  /**
   * Get direct access to the StoreUpdatesService with proper typing
   * @returns The StoreUpdatesService instance with its type
   */
  storeUpdates<T = any>(): T {
    return this.servicesRegistry.getService<T>('StoreUpdatesService');
  }

  /**
   * Get direct access to the StoreMigrationService with proper typing
   * @returns The StoreMigrationService instance with its type
   */
  storeMigrations<T = any>(): T {
    return this.servicesRegistry.getService<T>('StoreMigrationService');
  }

  /**
   * Get direct access to the StoreThirdPartyConnectionsService with proper typing
   * @returns The StoreThirdPartyConnectionsService instance with its type
   */
  storeThirdPartyConnections<T = any>(): T {
    return this.servicesRegistry.getService<T>('StoreThirdPartyConnectionsService');
  }

  /**
   * Get direct access to the UserService with proper typing
   * @returns The UserService instance with its type
   */
  users<T = UserService>(): T {
    return this.servicesRegistry.getService<T>('UserService');
  }

  /**
   * Get direct access to the ItemService with proper typing
   * @returns The ItemService instance with its type
   */
  items<T = ItemService>(): T {
    return this.servicesRegistry.getService<T>('ItemService');
  }

  /**
   * Get direct access to the PaymentService with proper typing
   * @returns The PaymentService instance with its type
   */
  payments<T = PaymentService>(): T {
    return this.servicesRegistry.getService<T>('PaymentService');
  }

  /**
   * Get direct access to the PaymentGettersService with proper typing
   * @returns The PaymentGettersService instance with its type
   */
  paymentGetters<T = PaymentGettersService>(): T {
    return this.servicesRegistry.getService<T>('PaymentGettersService');
  }

  /**
   * Get direct access to the PaymentMethodService with proper typing
   * @returns The PaymentMethodService instance with its type
   */
  paymentMethods<T = any>(): T {
    return this.servicesRegistry.getService<T>('PaymentMethodService');
  }

  /**
   * Get direct access to the WalletService with proper typing
   * @returns The WalletService instance with its type
   */
  wallets<T = WalletService>(): T {
    return this.servicesRegistry.getService<T>('WalletService');
  }

  /**
   *  Get direct access to the CountryService with proper typing
   * @returns The CountryService instance with its type
   */
  country<T = CountryService>(): T {
    return this.servicesRegistry.getService<T>('CountryService');
  }

  /**
   * Get direct access to the CurrencyConversionService with proper typing
   * @returns The CurrencyConversionService instance with its type
   */
  currencyConversion<T = any>(): T {
    return this.servicesRegistry.getService<T>('CurrencyConversionService');
  }

  /**
   * Get direct access to the CartService with proper typing
   * @returns The CartService instance with its type
   */
  cart<T = CartService>(): T {
    return this.servicesRegistry.getService<T>('CartService');
  }

  /**
   * Get direct access to the AnalyticService with proper typing
   * @returns The AnalyticService instance with its type
   */
  analytics<T = AnalyticService>(): T {
    return this.servicesRegistry.getService<T>('AnalyticService');
  }

  /**
   * Get direct access to the InvoiceService with proper typing
   * @returns The InvoiceService instance with its type
   */
  invoice<T = InvoiceService>(): T {
    return this.servicesRegistry.getService<T>('InvoiceService');
  }

  /**
   * Get direct access to the PlanService with proper typing
   * @returns The PlanService instance with its type
   */
  plans<T = PlanService>(): T {
    return this.servicesRegistry.getService<T>('PlanService');
  }

  /**
   * Get direct access to the SubscriptionService with proper typing
   * @returns The SubscriptionService instance with its type
   */
  subscription<T = SubscriptionService>(): T {
    return this.servicesRegistry.getService<T>('SubscriptionService');
  }

  /**
   * Get direct access to the DeliveryService with proper typing
   * @returns The DeliveryService instance with its type
   */
  deliveries<T = DeliveryService>(): T {
    return this.servicesRegistry.getService<T>('DeliveryService');
  }

  /**
   * Get direct access to the CatlogCreditsService with proper typing
   * @returns The CatlogCreditsService instance with its type
   */
  catlogCredits<T = any>(): T {
    return this.servicesRegistry.getService<T>('CatlogCreditsService');
  }

  /**
   * Get direct access to the BranchesService with proper typing
   * @returns The BranchesService instance with its type
   */
  branches<T = any>(): T {
    return this.servicesRegistry.getService<T>('BranchesService');
  }

  /**
   * Get direct access to the DeliveryAreaService with proper typing
   * @returns The DeliveryAreaService instance with its type
   */
  deliveryAreas<T = DeliveryAreaService>(): T {
    return this.servicesRegistry.getService<T>('DeliveryAreaService');
  }

  /**
   * Get direct access to the DomainService with proper typing
   * @returns The DomainService instance with its type
   */
  domains<T = any>(): T {
    return this.servicesRegistry.getService<T>('DomainService');
  }

  /**
   * Get direct access to the KYCService with proper typing
   * @returns The KYCService instance with its type
   */
  kyc<T = any>(): T {
    return this.servicesRegistry.getService<T>('KYCService');
  }

  /**
   * Get direct access to the AdminConfigService with proper typing
   * @returns The AdminConfigService instance with its type
   */
  adminConfig<T = any>(): T {
    return this.servicesRegistry.getService<T>('AdminConfigService');
  }

  /**
   * Get direct access to the AffiliatesService with proper typing
   * @returns The AffiliatesService instance with its type
   */
  affiliates<T = any>(): T {
    return this.servicesRegistry.getService<T>('AffiliatesService');
  }

  /**
   * Get direct access to the CustomerService with proper typing
   * @returns The CustomerService instance with its type
   */
  customers<T = CustomerService>(): T {
    return this.servicesRegistry.getService<T>('CustomerService');
  }

  /**
   * Get direct access to the IpAddressService with proper typing
   * @returns The IpAddressService instance with its type
   */
  ipAddress<T = any>(): T {
    return this.servicesRegistry.getService<T>('IpAddressService');
  }

  /**
   * Get direct access to the ReceiptsService with proper typing
   * @returns The ReceiptsService instance with its type
   */
  receipts<T = any>(): T {
    return this.servicesRegistry.getService<T>('ReceiptsService');
  }

  /**
   * Get direct access to the ItemExportImportService with proper typing
   * @returns The ItemExportImportService instance with its type
   */
  itemExportImport<T = any>(): T {
    return this.servicesRegistry.getService<T>('ItemExportImportService');
  }

  /**
   * Get direct access to the CouponService with proper typing
   * @returns The CouponService instance with its type
   */
  coupons<T = any>(): T {
    return this.servicesRegistry.getService<T>('CouponService');
  }

  /**
   * Get direct access to the DiscountService with proper typing
   * @returns The DiscountService instance with its type
   */
  discounts<T = any>(): T {
    return this.servicesRegistry.getService<T>('DiscountService');
  }

  /**
   * Register a service in the registry
   * @param serviceName The unique name for the service
   * @param serviceInstance The service instance
   */
  register<T>(serviceName: string, serviceInstance: T): void {
    this.servicesRegistry.register(serviceName, serviceInstance);
  }

  /**
   * Helper method to automatically register a service using its class name
   * @param serviceInstance The service instance to register
   * @returns The registered service instance
   */
  registerService<T>(serviceInstance: T): T {
    const constructor = serviceInstance.constructor;
    const serviceName = constructor.name;
    this.register(serviceName, serviceInstance);
    return serviceInstance;
  }

  /**
   * Get direct access to any registered service by name
   * @param serviceName The name of the service to retrieve
   * @returns The service instance with its type
   */
  service<T>(serviceName: string): T {
    return this.servicesRegistry.getService<T>(serviceName);
  }
}
