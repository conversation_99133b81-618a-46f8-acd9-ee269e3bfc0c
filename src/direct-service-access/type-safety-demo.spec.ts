import { Test, TestingModule } from '@nestjs/testing';
import { DirectServiceAccessService } from './direct-service-access.service';
import { ServicesRegistry } from '../service-registry';

describe('Type Safety Demo', () => {
  let directService: DirectServiceAccessService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DirectServiceAccessService, ServicesRegistry],
    }).compile();

    directService = module.get<DirectServiceAccessService>(DirectServiceAccessService);
  });

  it('should provide full type safety with IntelliSense', () => {
    // Mock CountryService with proper typing
    const mockCountryService = {
      getCountry: jest.fn().mockResolvedValue({
        code: 'NG',
        name: 'Nigeria',
        currency: 'NGN',
      }),
      getCountries: jest.fn().mockResolvedValue([]),
      addNewCountry: jest.fn().mockResolvedValue(undefined),
    };

    // Mock PlanService with proper typing
    const mockPlanService = {
      getPlansLean: jest.fn().mockResolvedValue([{ _id: '1', name: 'Basic', type: 'BASIC' }]),
      getPlans: jest.fn().mockResolvedValue([]),
      getPlan: jest.fn().mockResolvedValue(null),
      create: jest.fn().mockResolvedValue({}),
    };

    // Register the services
    directService.register('CountryService', mockCountryService);
    directService.register('PlanService', mockPlanService);

    // ✅ NOW YOU GET FULL TYPE SAFETY AND INTELLISENSE!

    // When you type: directService.country().
    // You should see IntelliSense suggestions for:
    // - getCountry(filter)
    // - getCountries()
    // - addNewCountry(country)
    const countryService = directService.country();
    expect(typeof countryService.getCountry).toBe('function');
    expect(typeof countryService.getCountries).toBe('function');
    expect(typeof countryService.addNewCountry).toBe('function');

    // When you type: directService.plans().
    // You should see IntelliSense suggestions for:
    // - getPlansLean(filter)
    // - getPlans(filter)
    // - getPlan(data)
    // - create(planReq)
    const planService = directService.plans();
    expect(typeof planService.getPlansLean).toBe('function');
    expect(typeof planService.getPlans).toBe('function');
    expect(typeof planService.getPlan).toBe('function');
    expect(typeof planService.create).toBe('function');
  });

  it('should show the before and after comparison', async () => {
    const mockCountryService = {
      getCountry: jest.fn().mockResolvedValue({ code: 'NG', name: 'Nigeria' }),
      getCountries: jest.fn(),
      addNewCountry: jest.fn(),
    };

    directService.register('CountryService', mockCountryService);

    // ❌ BEFORE (No type safety):
    // const result = await this.brokerTransport.send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, { code: 'NG' }).toPromise();
    // - No IntelliSense for parameters
    // - No compile-time checking
    // - Generic return type

    // ✅ AFTER (Full type safety):
    const country = await directService.country().getCountry({ code: 'NG' as any });
    // - Full IntelliSense for method parameters
    // - Compile-time parameter validation
    // - Typed return values

    expect(mockCountryService.getCountry).toHaveBeenCalledWith({ code: 'NG' });
    expect(country.name).toBe('Nigeria');
  });

  it('should demonstrate parameter validation', () => {
    const mockPlanService = {
      getPlansLean: jest.fn().mockResolvedValue([]),
      getPlans: jest.fn(),
      getPlan: jest.fn(),
      create: jest.fn(),
    };

    directService.register('PlanService', mockPlanService);

    // ✅ NOW TypeScript will validate your parameters!
    const planService = directService.plans();

    // If you try to call a method that doesn't exist, TypeScript will catch it:
    // planService.nonExistentMethod(); // ❌ TypeScript error!

    // If you pass wrong parameters, TypeScript will catch it:
    // planService.getPlansLean('wrong-type'); // ❌ TypeScript error if method expects object!

    // Valid calls work perfectly:
    planService.getPlansLean({}); // ✅ Valid
    expect(mockPlanService.getPlansLean).toHaveBeenCalledWith({});
  });

  it('should maintain type safety even with explicit generic typing', () => {
    // You can still use explicit typing when needed
    const mockService = {
      customMethod: jest.fn().mockReturnValue('custom result'),
    };

    directService.register('CustomService', mockService);

    // Explicit typing when you need specific interfaces
    type CustomServiceType = {
      customMethod(): string;
    };

    const customService = directService.service<CustomServiceType>('CustomService');
    const result = customService.customMethod();

    expect(result).toBe('custom result');
    expect(typeof customService.customMethod).toBe('function');
  });
});
