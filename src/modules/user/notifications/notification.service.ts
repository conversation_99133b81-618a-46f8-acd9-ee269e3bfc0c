import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, PaginateModel, Types } from 'mongoose';
import webPush from 'web-push';
import {
  DELIVERY_METHOD,
  FirebaseSubscription,
  FirebaseSubscriptionDocument,
  Notification,
  NotificationDocument,
  NOTIFICATION_TYPE,
  PushNotificationSubscription,
  PushNotificationSubscriptionDocument,
} from './notification.schema';
import { ConfigService } from '@nestjs/config';
import { COUNTRY_CODE } from '../../country/country.schema';
import { FirebaseRepository } from '../../../repositories/firebase.repository';
import { NOTIFICATION_PLATFORM } from '../../../models/dtos/UserDtos';

interface NotificationPayload {
  title: string;
  message: string;
  path: string;
  type: NOTIFICATION_TYPE;
  data?: Record<string, any>;
  store?: string;
  platform?: NOTIFICATION_PLATFORM;
}

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @InjectModel(Notification.name) private notificationModel: PaginateModel<NotificationDocument>,
    @InjectModel(PushNotificationSubscription.name)
    private pushNotificationSubscriptionModel: Model<PushNotificationSubscriptionDocument>,
    @InjectModel(FirebaseSubscription.name)
    private firebaseSubscriptionModel: Model<FirebaseSubscriptionDocument>,
    private readonly firebaseService: FirebaseRepository,
    private readonly configService: ConfigService,
  ) {
    // Set up web push with VAPID keys
    const vapidPublicKey = this.configService.get<string>('VAPID_PUBLIC_KEY');
    const vapidPrivateKey = this.configService.get<string>('VAPID_PRIVATE_KEY');
    if (vapidPublicKey && vapidPrivateKey) {
      webPush.setVapidDetails('mailto:<EMAIL>', vapidPublicKey, vapidPrivateKey);
    }
  }

  /**
   * Send notification to user(s) through selected platforms
   */
  async sendNotification(userId: string | Types.ObjectId, payload: NotificationPayload): Promise<NotificationDocument> {
    // For GENERIC notifications sent by admins, ensure we use a safe path that won't cause mobile app errors
    let notificationPath = payload.path;
    if (payload.type === NOTIFICATION_TYPE.GENERIC) {
      // Check if the path contains specific routes that might cause mobile app errors
      const problematicPatterns = ['/orders/', '?forCustomer=true', '/invoices?', '/deliveries/', 'showInvoice=true'];

      const hasProblematicPath = problematicPatterns.some((pattern) => payload.path.includes(pattern));

      if (hasProblematicPath) {
        // For generic admin messages, redirect to dashboard instead of specific resource pages
        notificationPath = '/dashboard';
        this.logger.log(
          `Redirecting GENERIC notification path from '${payload.path}' to '/dashboard' to prevent mobile app errors`,
        );
      }
    }

    // First create the notification record
    const notification = await this.notificationModel.create({
      user: userId.toString(),
      store: payload.store,
      title: payload.title,
      message: payload.message,
      path: notificationPath,
      type: payload.type,
      data: payload.data || {},
      delivery_methods: [],
      read: false,
    });

    // Determine which platforms to send notifications to
    const platform = payload.platform || NOTIFICATION_PLATFORM.BOTH;
    const shouldSendWeb = platform === NOTIFICATION_PLATFORM.WEB || platform === NOTIFICATION_PLATFORM.BOTH;
    const shouldSendMobile = platform === NOTIFICATION_PLATFORM.MOBILE || platform === NOTIFICATION_PLATFORM.BOTH;

    // Find subscriptions based on platform preferences
    const webPushSubscriptions = shouldSendWeb
      ? await this.pushNotificationSubscriptionModel.find({ user_id: userId.toString() }).exec()
      : [];

    const firebaseSubscriptions = shouldSendMobile
      ? await this.firebaseSubscriptionModel.find({ user: userId.toString() }).exec()
      : [];

    // Send notifications through available channels
    const deliveryMethods: DELIVERY_METHOD[] = [];

    // Send web push notifications
    if (shouldSendWeb && webPushSubscriptions.length > 0) {
      const webPushPromises = webPushSubscriptions.map(async (subscription) => {
        try {
          await webPush.sendNotification(
            {
              endpoint: subscription.endpoint,
              keys: {
                p256dh: subscription.public_key,
                auth: subscription.private_key,
              },
            },
            JSON.stringify({
              title: payload.title,
              message: payload.message,
              data: { path: notificationPath, ...payload.data },
            }),
          );
          return { status: 'fulfilled' };
        } catch (e: any) {
          if (e.statusCode === 410) {
            // Subscription is no longer valid, delete it
            await this.pushNotificationSubscriptionModel.deleteOne({ _id: subscription._id }).exec();
          }
          this.logger.error(`Failed sending web-push notification: ${e.message}`);
          return { status: 'rejected', error: e.message };
        }
      });

      const webPushResults = await Promise.all(webPushPromises);
      if (webPushResults.some((result) => result.status === 'fulfilled')) {
        deliveryMethods.push(DELIVERY_METHOD.WEB_PUSH);
      }
    }

    // Send firebase notifications (mobile)
    if (shouldSendMobile && firebaseSubscriptions.length > 0) {
      const firebaseTokens = firebaseSubscriptions.map((sub) => sub.fcm_token);
      payload.data = { ...payload.data, type: payload.type };
      const firebaseResponse = await this.firebaseService.sendToDevices(firebaseTokens, {
        title: payload.title,
        body: payload.message,
        path: notificationPath,
        data: payload.data,
      });

      // Handle invalid tokens deletion
      if (firebaseResponse.invalidTokens.length > 0) {
        try {
          const deleteResult = await this.firebaseSubscriptionModel.deleteMany({
            fcm_token: { $in: firebaseResponse.invalidTokens },
          });
          this.logger.log(`Deleted ${deleteResult.deletedCount} invalid Firebase tokens`);
        } catch (error) {
          this.logger.error(`Failed to delete invalid Firebase tokens: ${error.message}`);
        }
      }

      this.logger.log(`Firebase results: ${JSON.stringify(firebaseResponse.results)}`);

      if (firebaseResponse.results.some((result) => result.status === 'fulfilled')) {
        deliveryMethods.push(DELIVERY_METHOD.FIREBASE);
      }
    }

    // Update the notification with delivery methods
    if (deliveryMethods.length > 0) {
      await this.notificationModel.updateOne(
        { _id: notification._id },
        { $set: { delivery_methods: deliveryMethods } },
      );
      notification.delivery_methods = deliveryMethods;
    }

    this.logger.log(`Notification sent to platform(s): ${platform}. Delivery methods: ${deliveryMethods.join(', ')}`);

    return notification;
  }

  /**
   * Get notifications for a specific store
   */
  async getStoreNotifications(
    userId: string,
    storeId: string,
    page = 1,
    limit = 20,
  ): Promise<{
    data: { notifications: NotificationDocument[] };
    page: number;
    next_page: number | null;
    prev_page: number | null;
    total: number;
    total_pages: number;
    per_page: number;
  }> {
    const userObjectId = new Types.ObjectId(userId);

    // Filter by user and store, including notifications without store (for backward compatibility)
    const filter = {
      user: userObjectId.toString(),
      $or: [
        { store: storeId },
        { store: { $exists: false } }, // Include notifications without store for backward compatibility
        { store: null },
      ],
    };

    const paginatedResult = await this.notificationModel.paginate(filter, {
      sort: { createdAt: -1 },
      page: page,
      limit: limit,
      lean: true,
    });

    return {
      data: { notifications: paginatedResult.docs },
      page: paginatedResult.page,
      next_page: paginatedResult.nextPage,
      prev_page: paginatedResult.prevPage,
      total: paginatedResult.totalDocs,
      total_pages: paginatedResult.totalPages,
      per_page: paginatedResult.limit,
    };
  }

  /**
   * Get count of unread notifications for a specific store
   */
  async getStoreUnreadCount(userId: string, storeId: string): Promise<number> {
    const userObjectId = new Types.ObjectId(userId);

    const filter = {
      user: userObjectId.toString(),
      read: false,
      $or: [{ store: storeId }, { store: { $exists: false } }, { store: null }],
    };

    return this.notificationModel.countDocuments(filter);
  }

  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: string): Promise<NotificationDocument | null> {
    return this.notificationModel.findByIdAndUpdate(notificationId, { read: true }, { new: true }).exec();
  }

  /**
   * Mark all notifications as read for a specific store
   */
  async markAllStoreAsRead(userId: string, storeId: string): Promise<void> {
    const userObjectId = new Types.ObjectId(userId);

    const filter = {
      user: userObjectId.toString(),
      read: false,
      $or: [{ store: storeId }, { store: { $exists: false } }, { store: null }],
    };

    await this.notificationModel.updateMany(filter, { read: true }).exec();
  }

  /**
   * Save a new push notification subscription
   */
  async savePushSubscription(
    userId: string,
    subscription: {
      endpoint: string;
      public_key: string;
      private_key: string;
      country?: COUNTRY_CODE;
    },
  ): Promise<PushNotificationSubscriptionDocument> {
    const existingSubscription = await this.pushNotificationSubscriptionModel
      .findOne({ user_id: userId, endpoint: subscription.endpoint })
      .exec();

    if (existingSubscription) {
      return existingSubscription;
    }

    return this.pushNotificationSubscriptionModel.create({
      user_id: userId,
      endpoint: subscription.endpoint,
      public_key: subscription.public_key,
      private_key: subscription.private_key,
      country: subscription.country || COUNTRY_CODE.NG,
    });
  }

  /**
   * Save a new firebase subscription
   */
  async saveFirebaseSubscription(
    userId: string,
    fcmToken: string,
    country?: COUNTRY_CODE,
  ): Promise<FirebaseSubscriptionDocument> {
    const existingSubscription = await this.firebaseSubscriptionModel
      .findOne({ user: userId, fcm_token: fcmToken })
      .exec();

    if (existingSubscription) {
      return existingSubscription;
    }

    return this.firebaseSubscriptionModel.create({
      user: userId,
      fcm_token: fcmToken,
      country: country || COUNTRY_CODE.NG,
    });
  }
}
