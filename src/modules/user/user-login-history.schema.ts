import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { User } from './user.schema';

export type UserLoginHistoryDocument = UserLoginHistory & Document;

export enum LOGIN_SOURCE {
  WEB = 'WEB',
  MOBILE_APP = 'MOBILE_APP',
}

export enum DEVICE_TYPE {
  IOS = 'ios',
  ANDROID = 'android',
  WEB = 'web',
}

export enum EVENT_TYPE {
  LOGIN = 'login',
  SIGNUP = 'signup',
}

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class UserLoginHistory {
  _id: string;

  @ApiProperty({ description: 'User who logged in' })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  user: User | string;

  @ApiProperty({ description: 'Login source - mobile app or web' })
  @Prop({ type: String, enum: Object.values(LOGIN_SOURCE), required: true })
  login_source: LOGIN_SOURCE;

  @ApiProperty({ description: 'IP address of the login' })
  @Prop({ type: String })
  ip_address?: string;

  @ApiProperty({ description: 'User agent string' })
  @Prop({ type: String })
  user_agent?: string;

  @ApiProperty({ description: 'Device type - android or ios or web' })
  @Prop({ type: String, enum: Object.values(DEVICE_TYPE), required: false })
  device_type?: DEVICE_TYPE;

  @ApiProperty({ description: 'Event type - login or signup' })
  @Prop({ type: String, enum: Object.values(EVENT_TYPE), required: true })
  event_type: EVENT_TYPE;

  @ApiProperty({ description: 'Login timestamp' })
  @Prop({ type: Date, default: Date.now })
  created_at?: Date;
}

export const UserLoginHistorySchema = SchemaFactory.createForClass(UserLoginHistory);
