import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Attribution, AttributionDocument } from './attribution.schema';

@Injectable()
export class AttributionService {
  private readonly logger = new Logger(AttributionService.name);

  constructor(@InjectModel(Attribution.name) private attributionModel: Model<AttributionDocument>) {}

  /**
   * Track attribution data for a web visitor
   */
  async trackAttribution(ipAddress: string, sourceAd?: any): Promise<AttributionDocument> {
    try {
      const attributionData = {
        ip_address: ipAddress,
        source_ad: sourceAd || {},
      };

      const attribution = new this.attributionModel(attributionData);
      return await attribution.save();
    } catch (error) {
      this.logger.error('Error tracking attribution', error);
      throw error;
    }
  }

  /**
   * Associate attribution with a user
   */
  async associateWithUser(attributionId: string, userId: string): Promise<void> {
    try {
      await this.attributionModel.findByIdAndUpdate(attributionId, {
        $set: { user_id: userId },
      });
    } catch (error) {
      this.logger.error('Error associating attribution with user', error);
      throw error;
    }
  }

  /**
   * Get attribution by IP address with optional time window
   */
  async getAttributionByIp(ipAddress: string, hoursAgo?: number): Promise<AttributionDocument | null> {
    try {
      const query: any = { ip_address: ipAddress };

      // If hoursAgo is provided, add time filter
      if (hoursAgo && hoursAgo > 0) {
        const cutoffTime = new Date();
        cutoffTime.setHours(cutoffTime.getHours() - hoursAgo);
        query.created_at = { $gte: cutoffTime };
      }

      return await this.attributionModel.findOne(query).sort({ created_at: -1 }).exec();
    } catch (error) {
      this.logger.error('Error getting attribution by IP', error);
      throw error;
    }
  }

  /**
   * Get or create attribution by IP address with optional time window
   */
  async getOrCreateAttributionByIp(ipAddress: string, sourceAd?: any, hoursAgo?: number): Promise<AttributionDocument> {
    try {
      let attribution = await this.getAttributionByIp(ipAddress, hoursAgo);

      if (!attribution) {
        attribution = await this.trackAttribution(ipAddress, sourceAd);
      }

      return attribution;
    } catch (error) {
      this.logger.error('Error getting or creating attribution by IP', error);
      throw error;
    }
  }

  /**
   * Get attribution by user ID
   */
  async getAttributionByUserId(userId: string): Promise<AttributionDocument | null> {
    try {
      return await this.attributionModel.findOne({ user_id: userId }).exec();
    } catch (error) {
      this.logger.error('Error getting attribution by user ID', error);
      throw error;
    }
  }

  /**
   * Get all attributions by IP address within optional time window
   */
  async getAttributionsByIp(ipAddress: string, hoursAgo?: number): Promise<AttributionDocument[]> {
    try {
      const query: any = { ip_address: ipAddress };

      // If hoursAgo is provided, add time filter
      if (hoursAgo && hoursAgo > 0) {
        const cutoffTime = new Date();
        cutoffTime.setHours(cutoffTime.getHours() - hoursAgo);
        query.created_at = { $gte: cutoffTime };
      }

      return await this.attributionModel.find(query).sort({ created_at: -1 }).exec();
    } catch (error) {
      this.logger.error('Error getting attributions by IP', error);
      throw error;
    }
  }
}
