import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { Country, COUNTRY_CODE } from '../../country/country.schema';

class ReferredUser {
  @ApiProperty()
  @Prop({ type: Boolean })
  has_claimed: boolean;

  @ApiProperty()
  @Prop({ type: Number })
  times_claimed: number;

  @ApiProperty()
  @Prop({ type: String })
  user: string;

  @ApiProperty()
  @Prop({ type: Date })
  referred_on: Date;
}

class BonusConfig {
  @ApiProperty()
  @Prop({ type: String, enum: ['percentage', 'fixed'] })
  type: 'percentage' | 'fixed';

  @ApiProperty()
  @Prop({ type: Number })
  amount: number;
}

export class ReferralBonusConfig {
  @ApiProperty()
  @Prop({ type: BonusConfig })
  referrer: BonusConfig;

  @ApiProperty()
  @Prop({ type: BonusConfig })
  invitee: BonusConfig;

  @ApiProperty()
  @Prop({ type: String, enum: ['custom', 'default'] })
  type: 'custom' | 'default';
}

export type ReferralsDocument = Referrals & Document;

@Schema({ timestamps: true })
export class Referrals {
  public id: string;
  public updatedAt?: Date;
  public createdAt?: Date;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', unique: true })
  owner: string;

  @ApiProperty()
  @Prop({ type: String })
  referral_code: string;

  @ApiProperty()
  @Prop({ type: [{ has_claimed: Boolean, user: String, referred_on: Date, times_claimed: Number }] })
  referrals: ReferredUser[];

  @ApiProperty()
  @Prop({ type: ReferralBonusConfig })
  bonus_config: ReferralBonusConfig;

  @ApiProperty()
  @Prop({ type: String, ref: 'Country' })
  country: COUNTRY_CODE | Country;
}

export const ReferralsSchema = SchemaFactory.createForClass(Referrals);
