import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { SourceAd } from './user.schema';

export type AttributionDocument = Attribution & Document;

@Schema({ timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } })
export class Attribution {
  public id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  ip_address: string;

  @ApiProperty()
  @Prop({ type: Object, required: false })
  source_ad?: SourceAd;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: false })
  user_id?: string;

  @ApiProperty()
  @Prop({ type: Date })
  created_at: Date;

  @ApiProperty()
  @Prop({ type: Date })
  updated_at: Date;
}

export const AttributionSchema = SchemaFactory.createForClass(Attribution);
