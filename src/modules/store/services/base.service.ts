import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Store, StoreDocument } from '../store.schema';
import { Subscription } from '../../subscription/subscription.schema';
import { Plan } from '../../plan/plan.schema';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { BUSINESS_CATEGORIES } from '../utils/store-categories';
import { DirectServiceAccessService } from '../../../direct-service-access/direct-service-access.service';

@Injectable()
export default class BaseStoreService {
  constructor(
    @InjectModel(Store.name) protected readonly storeModel: Model<StoreDocument>,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly directService?: DirectServiceAccessService,
  ) {}

  async getAndValidateStore(storeId, projections = undefined, populate: boolean = false): Promise<StoreDocument> {
    let query = this.storeModel.findById(storeId, projections);

    // Conditionally populate the subscription and plan
    if (populate) {
      query = query.populate({
        path: 'subscription',
        populate: {
          path: 'plan',
        },
      });
    }

    const store = await query.exec();

    if (!store) {
      throw new BadRequestException('Store with id does not exist');
    }

    return store;
  }

  async getHasPaidSubscription(store: Store) {
    if (!store?.subscription) {
      store.subscription = await this.brokerTransport
        .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, { owner: store?.owner })
        .toPromise();
    }

    return ((store?.subscription as Subscription)?.plan as Plan)?.is_paid_plan;
  }
}
