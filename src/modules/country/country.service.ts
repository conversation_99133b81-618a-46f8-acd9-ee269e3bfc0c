import { Injectable, Logger, Post } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Country, CountryDocument, COUNTRY_CODE, CURRENCIES } from './country.schema';
import { DirectServiceAccessService } from '../../direct-service-access/direct-service-access.service';

export interface ICountry {
  name: string;
  currency: CURRENCIES;
  code: COUNTRY_CODE;
  dial_code: string;
  emoji: string;
}

@Injectable()
export class CountryService {
  constructor(
    @InjectModel(Country.name) private readonly countryModel: Model<CountryDocument>,
    private readonly logger: Logger,
    private readonly directService: DirectServiceAccessService,
  ) {
    // Register this service for direct access
    this.directService.register('CountryService', this);
  }

  async addNewCountry(country: ICountry) {
    await this.countryModel.create(country);
  }

  async getCountries(): Promise<Country[]> {
    return this.countryModel.find({});
  }

  async getCountry(filter: FilterQuery<Country>): Promise<Country> {
    return this.countryModel.findOne(filter).exec();
  }
}
