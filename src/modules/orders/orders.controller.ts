import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  HttpCode,
  HttpStatus,
  Req,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import {
  ConfirmOrderDto,
  CreateCustomerDto,
  CreateOrderDto,
  CustomerFilterDto,
  SellerCreateOrderDto,
  UpdateCustomerDto,
} from './dto/create-order.dto';
import { UpdateOrderDto, UpdateOrderFromCustomerDto, UpdateOrderFromSellerDto } from './dto/update-order.dto';
import {
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiHeader,
  ApiOkResponse,
  ApiQuery,
  ApiSecurity,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';

import { Order, ORDER_STATUSES } from './order.schema';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { IRequest, IResponse } from '../../interfaces/request.interface';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { Res } from '@nestjs/common';
import { Readable } from 'stream';
import { PlanGuard } from '../../guards/plan.guard';
import { SCOPES } from '../../utils/permissions.util';
import { PlanPermissions } from '../../decorators/permission.decorator';
import { CURRENCIES } from '../country/country.schema';
import {
  BulkOrderUpdateDto,
  BulkOrderUpdateResponseDto,
  BulkMarkAsPaidDto,
  BulkMarkAsPaidResponseDto,
  OrdersFilterQueryDto,
} from '../../models/dtos/order.dto';
import { checkIfUserOwnsStore } from '../../utils';
import { Store } from '../store/store.schema';

@ApiTags('Orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ type: Order })
  async create(@Body() createOrderDto: CreateOrderDto, @Req() req: IRequest) {
    const data = await this.ordersService.create(createOrderDto, req.headers.origin);

    return {
      message: 'Order created successfully',
      data,
    };
  }

  @Post('/seller')
  @PlanPermissions(SCOPES.ORDERS.RECORD_ORDERS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiSecurity('bearer')
  @ApiCreatedResponse({ type: Order })
  async createFromSeller(@Body() createOrderDto: SellerCreateOrderDto, @Req() req: IRequest) {
    const data = await this.ordersService.create(createOrderDto, req.headers.origin, true);

    return {
      message: 'Order created successfully',
      data,
    };
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: [Order] })
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
        status: {
          type: 'string',
          example: ORDER_STATUSES.PROCESSING,
        },
      },
    },
  })
  async getPaginatedOrders(
    @Req() req: IRequest,
    @Query() query: PaginatedQueryDto,
    @Query('filter') filter: OrdersFilterQueryDto,
  ) {
    filter.store = req.user.store.id;

    const data = await this.ordersService.getPaginatedOrders(filter, query);
    return {
      message: 'Order paginated successfully',
      data,
    };
  }

  @Get('/coupon/:coupon_code')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: [Order] })
  async getCouponOrders(@Req() req: IRequest, @Param('coupon_code') coupon_code: string) {
    const data = await this.ordersService.getCouponOrders(coupon_code);
    return {
      message: 'Orders fetched',
      data,
    };
  }

  @Post('confirm')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  async confirmOrder(@Req() req: IRequest, @Body() body: ConfirmOrderDto) {
    const data = await this.ordersService.confirmOrder(req.user.store.id, body.order, req.headers.origin);
    return {
      message: 'Order confirmed successfully',
      data,
    };
  }

  @Post(':id/generate-invoice')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  async generateInvoice(@Req() req: IRequest, @Param('id') orderId: string) {
    const data = await this.ordersService.generateInvoiceFromOrder(orderId, req.user.store.id);
    return {
      message: 'Invoice generated successfully',
      data,
    };
  }

  @Get('preview/:id')
  @HttpCode(HttpStatus.OK)
  @ApiHeader({ name: 'authorization' })
  @ApiQuery({ name: 'phone', required: false })
  @ApiOkResponse({ type: Order })
  async previewOrder(@Req() req: IRequest, @Param('id') orderId: string, @Query('phone') phone: string) {
    const token = ((req.headers['authorization'] as string) || '').replace('Bearer ', '');
    const data = await this.ordersService.previewOrder(orderId, {
      token: (token || '').trim(),
      phone: (phone || '').trim(),
    });
    return {
      message: 'Order fetched successfully',
      data,
    };
  }

  @Get('export')
  @PlanPermissions(SCOPES.ORDERS.EXPORT_ORDERS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiSecurity('bearer')
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
        currency: {
          type: 'string',
        },
        status: {
          type: 'string',
        },
      },
    },
  })
  async exportOrders(@Req() req: IRequest, @Query() query: any, @Res() res: IResponse) {
    const storeId = req.user.store.id;
    const filter = query.filter;
    const buffer = await this.ordersService.exportOrders(storeId, filter);
    const readable = new Readable();
    readable._read = () => {};
    readable.push(buffer);
    readable.push(null);
    readable.pipe(res);
    return {};
  }

  @Get('statistics')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
      },
    },
  })
  async getStats(@Req() req: IRequest, @Query() query: any) {
    const storeId = req.user.store.id;
    const filter = query.filter;

    const data = await this.ordersService.getOrderStats(storeId, filter);
    return {
      message: 'Order stats fetched successfully',
      data,
    };
  }

  @Put('bulk-mark-as-paid')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: BulkMarkAsPaidResponseDto })
  @ApiCreatedResponse({
    description: 'Bulk mark orders as paid',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: { $ref: getSchemaPath(BulkMarkAsPaidResponseDto) },
      },
    },
  })
  async bulkMarkOrdersAsPaid(@Body() bulkMarkAsPaidDto: BulkMarkAsPaidDto, @Req() req: IRequest) {
    const data = await this.ordersService.bulkMarkOrdersAsPaid(req.user.store.id, bulkMarkAsPaidDto.order_ids);

    return {
      message: data.message,
      data,
    };
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: Order })
  async findOne(@Param('id') id: string, @Req() req: IRequest) {
    const data = await this.ordersService.findOne(id, req.user.store.id);
    return {
      message: 'Order has been fetched successfully',
      data,
    };
  }

  @Put('bulk-update')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async bulkUpdateOrders(@Req() req: IRequest, @Body() updateDto: BulkOrderUpdateDto) {
    const data = await this.ordersService.bulkUpdateStatus(req.user.store.id, updateDto, req.headers.origin);
    return {
      message: 'Orders updated successfully',
      data,
    };
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  update(@Req() req: IRequest, @Param('id') id: string, @Body() updateOrderDto: UpdateOrderDto) {
    return this.ordersService.updateStatus(req.user.store.id, id, updateOrderDto, req.headers.origin, true);
  }

  @Put(':id/payment')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async markOrderAsPaid(@Req() req: IRequest, @Param('id') id: string) {
    const data = await this.ordersService.markOrderAsPaid({ _id: id, store: req.user.store.id }, true);

    if (data.error) {
      throw new BadRequestException(data.error);
    }

    return {
      message: 'Order updated successfully',
      data,
    };
  }

  @Put(':id/seller')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async updateFromSeller(@Req() req: IRequest, @Param('id') id: string, @Body() payload: UpdateOrderFromSellerDto) {
    const order = await this.ordersService.updateOrderFromSeller(id, payload, req.user.store.id);
    return {
      message: 'Order was updated successfully',
      data: order,
    };
  }

  @Put(':id/customer')
  @HttpCode(HttpStatus.OK)
  @ApiQuery({ name: 'phone', required: false })
  @ApiOkResponse({ type: Order })
  async updateFromCustomer(@Param('id') orderId: string, @Body() payload: UpdateOrderFromCustomerDto) {
    // payload.phone = phone;
    const data = await this.ordersService.updateOrderFromCustomer(orderId, payload);

    return {
      message: 'Order was updated successfully',
      data,
    };
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: Order })
  remove(@Req() req: IRequest, @Param('id') id: string) {
    return this.ordersService.remove(id, req.user.store.id);
  }

  @Post('/migrate-order-delivery-to-delivery-info')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreCheckoutChannels() {
    await this.ordersService.migrateOrderDeliveryToDeliveryInfo();
    return {
      message: 'migrated order delivery to delivery info',
      data: {},
    };
  }

  @Post('/migrate-order-currencies')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateOrderCurrencies() {
    const data = await this.ordersService.migrateOrderCurrencies();

    return {
      message: 'migrated order currencies',
      data,
    };
  }

  @Post('/migrate-customer-phones')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateCustomerPhones() {
    const data = await this.ordersService.migrateCustomerPhoneToFormatted();

    return {
      message: 'migrated customer phones',
      data,
    };
  }

  @Post('/migrate-item-total-orders')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateItemTotalOrders() {
    await this.ordersService.migrateTotalOrdersForItems();

    return {
      message: 'Migration completed successfully',
    };
  }

  @Post('update-order-item-image-url-to-new-s3-url')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateOrderItemImageUrlToNewS3Url() {
    this.ordersService.updateOrderItemImageUrlToNewS3Url();
    return {
      message: 'Migration process started successfully',
    };
  }

  @Post('migrate-validated-delivery-address')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateValidatedDeliveryAddress() {
    this.ordersService.validatedDeliveryAddressMigration();
    return {
      message: 'Migration process started successfully',
    };
  }
}
