import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
  PreconditionFailedException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateOrderDto, SellerCreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto, UpdateOrderFromCustomerDto, UpdateOrderFromSellerDto } from './dto/update-order.dto';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import {
  Order,
  ORDER_STATUSES,
  ORDER_STATUSES_CONFIG,
  OrderDocument,
  OrderFee,
  DELIVERY_METHODS,
  OrderItem,
  ORDER_CHANNELS,
} from './order.schema';
import mongoose, { FilterQuery, PaginateModel, Types } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { Customer, CustomerDocument, CustomerOrigin } from './customers/customer.schema';
import {
  checkIfUserOwnsStore,
  formatPhoneNumber,
  genChars,
  getDiscountPrice,
  mapPaginatedResponse,
  mapPaginateQuery,
  resolveCategories,
  paymentsEnabledGuard,
  toCurrency,
  amountFormat,
  isValidObjectId,
} from '../../utils';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Item } from '../item/item.schema';
import { CustomCheckoutFormItem, Store } from '../store/store.schema';
import {
  COUNTRY_CODE,
  COUNTRY_CURRENCY_MAP,
  CURRENCIES,
  CURRENCY_COUNTRY_MAP,
  Country,
} from '../country/country.schema';
import { Coupon, DiscountDocument } from '../item/discount-coupons/discounts-coupons.schema';
import { User } from '../user/user.schema';
import { FEE_TYPES, ORDER_PAID_TAG } from '../../enums/order.enum';
import {
  computeCouponDiscount,
  couponIsValid,
  filterItemsWithPricedOptions,
  findMatchingOptionsWithPrice,
} from './utils';
import { UserMessagingRepository } from '../../repositories/user-messaging.repository';
import { Invoice } from '../invoice/invoice.schema';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { STORE_PUBLIC_HIDDEN_INFO } from '../../utils/constants';
import { ShipbubbleRepository, ValidateAddressResponse } from '../../repositories/shipbubble.repository';
import { Address, AddressDocument } from '../deliveries/deliveries.schema';
import xlsx from 'json-as-xlsx';
import { Errorable } from '../../utils/types';
import {
  arrayToSentence,
  getDocId,
  getExchangeRates,
  stripUndefinedAndNull,
  getProductItemThumbnail,
} from '../../utils/functions';
import dayjs from 'dayjs';
import { RpcException } from '@nestjs/microservices';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { GoogleMapsRepository } from '../../repositories/maps.google.repository';
import { Cart } from '../cart/cart.schema';
import { BulkOrderUpdateDto, OrdersFilterQueryDto } from '../../models/dtos/order.dto';
import { CustomerIoRepository } from '../../repositories/customer-io.repository';
import { CurrencyRates } from '../wallets/currency-conversion/currency-rate.schema';
import e from 'express';
import { CurrencyConversionRepository } from '../../repositories/currency-conversions.repository';
import { ResendRepository } from '../../repositories/resend.repository';
import { OrdersWrapData } from '../store/year-wrap/year-wrap.service';
import { PaymentGettersService } from '../payment/services/payment.getters.service';
import { EmailOrderData } from '../../react-email/emails/utils/types';
import { Affiliate } from '../affiliates/affiliates.schema';
import { stat } from 'fs';
import { NOTIFICATION_TYPE } from '../user/notifications/notification.schema';
import { TieredPricing } from '../item/tiered-pricing/tiered-pricing.schema';
import { DeliveryArea } from '../store/delivery-areas/delivery-areas.schema';
// import { ORDER_JOB_TYPES } from './order.queue';
// const { inspect } = require('node:util');

const DEFAULT_PAYMENT_TIMEOUT = 20;

@Injectable()
export class OrdersService {
  constructor(
    @InjectModel(Order.name)
    public readonly orderModel: PaginateModel<OrderDocument>,
    @InjectModel(Customer.name)
    private readonly customerModel: PaginateModel<CustomerDocument>,
    @InjectConnection() protected readonly connection: mongoose.Connection,
    public readonly brokerTransport: BrokerTransportService,
    private readonly shipbubble: ShipbubbleRepository,
    protected readonly googleMapsRepository: GoogleMapsRepository,
    public readonly resend: ResendRepository,
    public readonly customerIoRepository: CustomerIoRepository,
    public readonly currencyConversion: CurrencyConversionRepository,
    private readonly paymentGetterService: PaymentGettersService,
    @InjectQueue(QUEUES.ORDER) private ordersQueue: Queue,
    private readonly logger: Logger,
  ) {
    this.logger.setContext('OrdersService');
  }

  async create(
    orderReq: SellerCreateOrderDto | CreateOrderDto,
    url: string,
    fromSeller: boolean = false,
    fromRemote = false,
  ) {
    let discountAmount = 0;
    let coupon: Coupon;
    let deliveryArea: DeliveryArea;
    let fees: OrderFee[];
    let parsedAffiliate: Affiliate;

    const cart = orderReq?.cart;
    delete orderReq?.cart;

    const isChowbotOrder = orderReq?.channel === ORDER_CHANNELS.CHATBOT;

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: orderReq.store })
      .toPromise();

    if (store.disabled) {
      throw new BadRequestException("This store is disabled and can't accept orders");
    }

    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (store.configuration?.require_emails && !orderReq.customer.email && !isChowbotOrder && fromSeller === false) {
      throw new BadRequestException('Please provide an email address');
    }

    if (orderReq?.affiliate) {
      parsedAffiliate = await this.brokerTransport
        .send<Affiliate>(BROKER_PATTERNS.AFFILIATE.GET_AFFILIATE, { slug: orderReq.affiliate })
        .toPromise();
    }

    //VALIDATE ADDRESS
    let validatedAddress: { error?: string; data?: ValidateAddressResponse };

    if (
      store?.deliveries_enabled &&
      orderReq?.delivery_method !== DELIVERY_METHODS.PICKUP &&
      orderReq?.delivery_method !== DELIVERY_METHODS.NONE &&
      !isChowbotOrder &&
      orderReq?.customer?.name
    ) {
      try {
        const googleMapsLookup = await this.googleMapsRepository.lookupAddress(orderReq.delivery_info.delivery_address);
        validatedAddress = await this.shipbubble.validateAddress({
          address: googleMapsLookup.data,
          email: orderReq.customer?.email ?? '<EMAIL>',
          name: orderReq.delivery_info?.name,
          phone: orderReq.delivery_info?.phone,
        });
      } catch (e) {
        //do nothing so user can still place thier orders
      }
    }

    if (orderReq?.currency !== store?.currencies.products && !orderReq?.rates && !fromSeller && !fromRemote) {
      throw new BadRequestException('Please provide a currency rates id');
    }

    const currencyOptions = store?.currencies?.storefront;

    if (!currencyOptions.includes(orderReq.currency)) {
      throw new BadRequestException('Invalid currency selected');
    }

    const exchangeRates = await this.currencyConversion.getExchangeRates(
      orderReq?.rates,
      [orderReq.currency],
      store?.currencies?.products,
    );

    if (!exchangeRates) {
      throw new BadRequestException('Invalid currency rates id');
    }

    const convertCurrency = (amount: number) =>
      this.currencyConversion.convertToCurrency(amount, orderReq?.currency, exchangeRates, store?.currencies?.rates);

    if (orderReq.items.length == 0) {
      throw new BadRequestException('Please select some items for your order');
    }

    orderReq.items = await this.resolveItems(orderReq.items as any, store, true);

    if (orderReq.items.length == 0) {
      throw new BadRequestException('Something went wrong on our end');
    }

    if (
      store.delivery_areas.length > 0 &&
      !orderReq.delivery_info?.area &&
      orderReq.delivery_method === DELIVERY_METHODS.DELIVERY
    ) {
      throw new BadRequestException('Please select a delivery area');
    }

    this.validateMinimumOrderQuantity(orderReq.items);

    let totalAmount = this.calculateTotalOrderAmount(orderReq.items, convertCurrency);

    //RESOLVE DISCOUNT
    const couponCode = orderReq.coupon;

    if (couponCode && couponCode !== '') {
      const db_coupon = await this.getCoupon(couponCode, orderReq.store);

      if (!this.validateCoupon(db_coupon)) {
        throw new BadRequestException('Coupon is invalid or expired. Remove or change it');
      } else if (
        convertCurrency(db_coupon?.minimum_order_amount) &&
        totalAmount < convertCurrency(db_coupon?.minimum_order_amount)
      ) {
        throw new BadRequestException(
          `To use this coupon, you must make a purchase of at least ${toCurrency(
            convertCurrency(convertCurrency(db_coupon?.minimum_order_amount)),
            orderReq?.currency,
          )}`,
        );
      } else {
        discountAmount = computeCouponDiscount(totalAmount, db_coupon, convertCurrency);
        coupon = db_coupon;
      }
    }

    // this.addOrderToMilestoneQueue(orderReq, store.owner as any);

    // RESOLVE DELIVERY AREA
    if (orderReq?.delivery_info?.area) {
      deliveryArea = await this.brokerTransport
        .send<DeliveryArea>(BROKER_PATTERNS.STORE.GET_DELIVERY_AREA, {
          _id: orderReq?.delivery_info?.area,
        })
        .toPromise();

      delete orderReq.delivery_info.area;
    }

    //RESOLVE FEES AND TOTAL AMOUNT
    const discountFee = (orderReq?.fees ?? [])?.find((f) => f.type === FEE_TYPES.DISCOUNT)?.amount ?? 0;
    const vatFee = (orderReq?.fees ?? [])?.find((f) => f.type === FEE_TYPES.VAT)?.amount ?? 0;
    const deliveryFee = (orderReq?.fees ?? [])?.find((f) => f.type === FEE_TYPES.DELIVERY)?.amount ?? 0;
    const paymentFee = (orderReq?.fees ?? [])?.find((f) => f.type === FEE_TYPES.PAYMENT)?.amount ?? 0;

    //extract fees from extra details
    const itemsWithPricedOptions: CustomCheckoutFormItem[] = filterItemsWithPricedOptions(
      store?.configuration?.custom_checkout_form ?? [],
    );
    const feesFromExtraDetails =
      findMatchingOptionsWithPrice(itemsWithPricedOptions, orderReq?.extra_details ?? {}) ?? [];

    fees = this.getFees(
      { coupon, amount: discountAmount },
      deliveryArea,
      convertCurrency,
      discountFee, //discount will come in currency, don't convert
      vatFee,
      deliveryFee,
      paymentFee,
      feesFromExtraDetails,
    );
    totalAmount = this.calculateTotalAmount(fees, totalAmount);

    const session = await this.connection.startSession();
    session.startTransaction();

    /* --------- NO UPDATES HAPPEN BEFORE THIS POINT -------------- */

    //UPDATE ITEM QUANTITIES
    // if (orderReq.items.some((i) => i.item.quantity > -1)) {
    const updateResponse = await this.brokerTransport
      .send<{ error: boolean; items: any[] }>(BROKER_PATTERNS.ITEM.TRACK_ITEM_QUANTITIES, {
        items: orderReq.items.map((i) => ({
          itemId: i.item_id,
          quantity: i.quantity,
          variantIndex:
            i.variant_id &&
            i.snapshot.variants.options.findIndex((o) => getDocId(o)?.toString() === i.variant_id.toString()),
          isAlwaysAvailable: i.snapshot?.is_always_available || !i.snapshot.quantity,
        })),
      })
      .toPromise();

    if (updateResponse?.error) {
      await session.abortTransaction();
      if (fromRemote) throw new RpcException({ outOfStockError: true });
      throw new PreconditionFailedException('Some items are over the available quantity, please reload page');
    }
    // }

    //CREATE AND SAVE CUSTOMER
    let customer = await this.customerModel.findOne({
      phone: orderReq.customer.phone,
      store: store?.id as any,
    });

    if (!customer) {
      customer = new this.customerModel({
        ...orderReq.customer,
        store: orderReq?.store,
        ...(parsedAffiliate && { 'meta.affiliate': getDocId(parsedAffiliate) }),
        origin: fromSeller
          ? CustomerOrigin.MANUAL
          : orderReq?.channel === ORDER_CHANNELS.CHATBOT
          ? CustomerOrigin.CHOWBOT
          : CustomerOrigin.STOREFRONT,
      });
      customer.save({ session });
    } else {
      await customer.update(stripUndefinedAndNull(orderReq.customer), { session }).exec();
    }

    // SAVE DELIVERY ADDRESS
    let addressDocument: AddressDocument;

    if (validatedAddress?.data) {
      addressDocument = await this.brokerTransport
        .send<AddressDocument>(BROKER_PATTERNS.DELVERIES.SAVE_ADDRESS, {
          store: store.id,
          validatedAddress: validatedAddress?.data,
          phone: customer?.phone,
          customer: customer?.id,
        })
        .toPromise();
    }

    //convert to currency
    // fees = fees.map((f) => ({ ...f, amount: convertCurrency(f.amount) }));
    orderReq.items = this.currencyConversion.convertItemsToCurrency(orderReq.items, convertCurrency);

    const order = new this.orderModel({
      ...orderReq,
      _id: await this.generateOrderID(),
      total_amount: totalAmount,
      timeline: [{ status: ORDER_STATUSES.PENDING, time: new Date() }],
      customer: customer?._id,
      fees: fees,
      coupon_code: coupon?.coupon_code.toUpperCase(),
      delivery_info: orderReq?.delivery_info
        ? {
            ...orderReq.delivery_info,
            user_provided_address:
              orderReq?.delivery_info?.user_provided_address ?? orderReq?.delivery_info?.delivery_address,
            delivery_address: addressDocument?.formatted_address ?? orderReq?.delivery_info?.delivery_address,
            delivery_area: deliveryArea
              ? {
                  ...deliveryArea,
                  fee: deliveryFee ? 0 : convertCurrency(deliveryArea?.fee),
                }
              : null,
          }
        : null,
      currency: orderReq?.currency,
      validated_delivery_address: orderReq?.validated_delivery_address ?? addressDocument?.id,
      channel: orderReq?.channel ?? ORDER_CHANNELS.STORE_FRONT,
      from_seller: fromSeller,
      discount_amount: discountAmount,
      is_paid: (orderReq as SellerCreateOrderDto)?.is_paid ?? false,
      status: (orderReq as SellerCreateOrderDto)?.status ?? ORDER_STATUSES.PENDING,
      affiliate: parsedAffiliate ? getDocId(parsedAffiliate) : null,
    });

    customer = {
      ...customer.toJSON(),
      email: orderReq.customer.email,
      phone: orderReq.customer.phone,
      name: orderReq.customer.name,
    }; //force update customer object to be returned with the new customer details

    if (coupon) {
      await this.brokerTransport
        .send<Coupon>(BROKER_PATTERNS.ITEM.TRACK_ORDER_COUPON, {
          order_id: order._id,
          coupon_code: coupon.coupon_code,
          store: orderReq.store,
          customer: getDocId(customer),
        })
        .toPromise();
    }

    await order.save({ session });
    await session.commitTransaction();

    if (cart) {
      const updatedCart = await this.brokerTransport
        .send<Cart>(BROKER_PATTERNS.CART.UPDATE_CART, {
          id: cart,
          data: {
            order: getDocId(order),
          },
        })
        .toPromise();
    }

    // SEND EMAILS AND NOTIFICATIONS
    if (!fromSeller) {
      // TODO: Move to its own function
      await this.brokerTransport
        .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
          message: {
            title: 'You have a new order - Pending Payment 📦',
            message: `${customer.name} just ordered items worth ${toCurrency(
              order.total_amount.toString(),
              order.currency,
            )}`,
            path: `/orders/${order.id}?forCustomer=true`,
          },
          owner_only: false,
          store: orderReq.store,
          notification_type: NOTIFICATION_TYPE.NEW_ORDER,
          data: {
            id: order.id,
            store_id: orderReq.store,
            customer_name: customer.name,
            customer_phone: formatPhoneNumber(customer.phone),
          },
        })
        .toPromise();

      const orderCopy = { ...order.toJSON(), customer, store };
      const storeCurrency = orderCopy?.store?.currencies?.default ?? COUNTRY_CURRENCY_MAP[store.country as any];

      const emailData: EmailOrderData = {
        name: storeOwner.name.split(' ')[0],
        preview_text: `You have a new order from ${customer.name} - Pending Payment 📦`,
        store_name: store.name,
        store_logo: store?.logo,
        order: {
          order_id: order.id,
          link: `${process.env.CATLOG_WWW}/orders/${order.id}`,
          payment_link: `${process.env.CATLOG_WWW}/pay/${order.id}?byCustomer=true`,
          order_status: order.status,
          products: order.items.map(({ snapshot: item, quantity, variant }) => ({
            image: getProductItemThumbnail(item),
            name: item?.name,
            price: variant ? variant?.price : item.price,
            quantity: quantity,
          })),
          total: order.total_amount,
          customer_info: {
            name: customer?.name,
            phone: customer.phone,
            email: customer?.email,
          },
          fees: order.fees.map((fee) => ({
            label: fee.label,
            amount: fee.amount,
          })),
          delivery_method: order.delivery_method,
          delivery_area: order?.delivery_info?.delivery_area?.name,
          delivery_address: order?.delivery_info?.delivery_address,
          currency: order.currency,
        },
      };

      this.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_RECEIVED, {
        to: storeOwner.email,
        subject: 'You have a new order - Pending Payment 📦',
        data: emailData,
      });

      this.sendCustomerStatusMessage(orderCopy, url);
    }

    if (store?.payments_enabled) {
      const invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.FROM_ORDER, {
          ...order.toJSON(),
          customer,
        })
        .toPromise();

      order.invoice = invoice?.invoice_id;
      order.save();
    }

    const jsonOrder = order.toJSON();
    jsonOrder.items = await this.resolveItems(jsonOrder.items);

    if (
      !isChowbotOrder &&
      !order.from_seller &&
      store.configuration.payment_validates_order === true &&
      store.payments_enabled &&
      store?.configuration?.direct_checkout_enabled
    ) {
      await this.addOrderToCheckPaymentQueue(
        order,
        url,
        store?.configuration?.payment_timeout ? Number(store?.configuration?.payment_timeout) : undefined,
      );
    }

    await this.addOrderToMilestoneQueue(order, store.owner as any);

    try {
      if (store.configuration.low_stock_notifications_enabled === true) {
        await this.brokerTransport
          .send(BROKER_PATTERNS.ITEM.CHECK_STOCK_THRESHOLD, {
            orderId: getDocId(order),
          })
          .toPromise();
      }
    } catch (error) {
      console.error('Error checking stock threshold:', error);
    }

    return { ...jsonOrder, customer, store: this.removeStorePrivateInfo(store) };
  }

  async addOrderToCheckPaymentQueue(order: Order, url: string, timeout = DEFAULT_PAYMENT_TIMEOUT) {
    await this.ordersQueue.add(
      QUEUES.ORDER,
      {
        type: JOBS.CHECK_ORDER_PAYMENT,
        data: {
          order_id: order._id,
          url,
        },
      },
      {
        delay: 1000 * 60 * timeout,
      },
    );
  }

  async addOrderToMilestoneQueue(order: Order, storeOwner: string) {
    await this.ordersQueue.add(
      QUEUES.ORDER,
      {
        type: JOBS.ORDER_VOLUME_MILESTONE,
        data: {
          store: order.store,
          currency: order?.currency,
          user: storeOwner,
          order_id: order._id,
        },
      },
      {
        delay: 1000 * 60 * 60 * 6,
      },
    );

    await this.ordersQueue.add(
      QUEUES.ORDER,
      {
        type: JOBS.ORDER_COUNT_MILESTONE,
        data: {
          store: order?.store,
          user: storeOwner,
          order_id: order._id,
        },
      },
      {
        delay: 1000 * 60 * 60 * 6,
      },
    );
  }

  findAll() {
    return `This action returns all orders`;
  }

  async getOrderMilestones(store: string, currency: CURRENCIES) {
    const volume = await this.getTotalStoreOrderAmount({
      currency,
      store: new mongoose.Types.ObjectId(store) as any,
    });

    const total = await this.orderModel.countDocuments({
      store,
      status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
    });

    return {
      volume,
      total,
    };
  }

  async getTotalStoreOrderAmount(filter: FilterQuery<Order>) {
    const result = await this.orderModel
      .aggregate([
        {
          $match: {
            ...filter,
            is_deleted: { $ne: true },
            status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
          },
        },
        {
          $group: {
            _id: null,
            total: {
              $sum: '$total_amount',
            },
          },
        },
      ])
      .exec();

    return result[0]?.total as number;

    // return await this.orderModel.find({ ...filter, status: { $ne: ORDER_STATUSES.CANCELLED } }).then((orders) =>
    //   orders.reduce((accum, order) => {
    //     accum += order.total_amount;
    //     return accum;
    //   }, 0),
    // );
  }

  constructFilterQuery(filterQueryDto: OrdersFilterQueryDto) {
    const {
      channel,
      customer,
      from,
      to,
      fulfillment_method,
      payment_status,
      products,
      search,
      status,
      store,
      ...genericFilters
    } = filterQueryDto;

    const filters: FilterQuery<Order> = {
      ...genericFilters,
      is_deleted: { $ne: true },
      store: store as any,
    };

    if (from || to) {
      filters.created_at = {
        $gte: new Date(from ?? new Date()),
        $lte: new Date(to ?? new Date()),
      };
    }

    if (search) {
      filters._id = new RegExp(search, 'ig');
    }

    if (channel) {
      filters.channel = channel;
    }

    if (customer && isValidObjectId(customer)) {
      filters.customer = customer as any;
    }

    if (fulfillment_method) {
      filters.delivery_method = fulfillment_method;
    }

    if (payment_status) {
      switch (payment_status) {
        case ORDER_PAID_TAG.PAID:
          filters.is_paid = true;
          break;

        case ORDER_PAID_TAG.UNPAID:
          filters.is_paid = false;
          break;
      }
    }

    if (products && products.length > 0) {
      filters.items = {
        $elemMatch: {
          item_id: {
            $in: products.map((p) => new mongoose.Types.ObjectId(p)),
          },
        },
      };
    }

    if (status) {
      filters.status = status;
    }

    return filters;
  }

  async getPaginatedOrders(filterQueryDto: OrdersFilterQueryDto, paginationQuery: PaginatedQueryDto) {
    const filter = this.constructFilterQuery(filterQueryDto);

    if (paginationQuery.sort === 'ASC') {
      paginationQuery.sort = { created_at: 1 };
    }

    if (paginationQuery.sort === 'DESC') {
      paginationQuery.sort = { created_at: -1 };
    }

    const result = await this.orderModel.paginate(
      { ...filter, is_deleted: { $ne: true } },
      {
        ...mapPaginateQuery(paginationQuery),
        populate: ['customer'],
      },
    );

    result.docs = (await Promise.all(
      result.docs.map(async (doc) => {
        doc = doc.toJSON();
        doc.items = (await this.resolveItems(doc.items, null, false, false)) as any;
        return doc;
      }),
    )) as any;

    return {
      data: result.docs,
      ...mapPaginatedResponse(result),
    };
  }

  async getCouponOrders(coupon_code: string) {
    const orders = await this.orderModel
      .find(
        {
          coupon_code: new RegExp(`^${coupon_code.toLowerCase()}$`, 'i'),
          data: { $ne: true },
          is_deleted: { $ne: true },
        },
        { _id: 1, items: 1, total_amount: 1, currency: 1 },
      )
      .sort({ created_at: -1 })
      .lean();

    return orders;
  }

  async findOne(id: string, storeId: string) {
    const order = (
      await this.orderModel
        .findById(id)
        .populate('customer')
        .populate('store', 'categories')
        .populate('delivery', 'status')
        .populate('validated_delivery_address')
    )
      .populate({
        path: 'delivery_info',
        populate: { path: 'address', model: 'Address' },
      })
      .toJSON();
    checkIfUserOwnsStore(order.store as Store, storeId);
    // order.items = await this.resolveItems(order.items, order.store);
    if (!order || order.is_deleted) {
      throw new NotFoundException('Order not found');
    }
    return order;
  }

  async bulkUpdateStatus(storeId: string, dto: BulkOrderUpdateDto, url: string) {
    const promises = dto.orders.map(async (order) => {
      return this.updateStatus(storeId, order.id, { status: order.status, reason: order?.reason }, url);
    });
    return await Promise.all(promises);
  }

  async updateStatus(
    storeId: string,
    id: string,
    { status, reason }: UpdateOrderDto,
    url: string,
    notifyCustomerViaBot = false,
    notifySellerViaBot = true,
  ) {
    const session = await this.connection.startSession();
    session.startTransaction();

    const order = await this.orderModel
      .findOneAndUpdate(
        { _id: id, store: storeId, is_deleted: { $ne: true } },
        { status, cancellation_reason: reason ?? '', $push: { timeline: { status, time: new Date() } } },
        {
          new: true,
          session,
        },
      )
      .populate('store')
      .populate('customer');

    if (!order) {
      throw new BadRequestException("Order can't be updated cause it was not found");
    }

    checkIfUserOwnsStore(order.store as Store, storeId);

    if ([ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED].includes(status) && order) {
      if (order.items.some((item) => item.snapshot.quantity > -1)) {
        const itemsResponse = await this.brokerTransport
          .send<{ error: boolean; items: any[] }>(BROKER_PATTERNS.ITEM.TRACK_ITEM_QUANTITIES, {
            items: order.items
              .filter((i) => i.snapshot?.quantity > -1)
              .map((i) => ({
                itemId: i.item_id,
                quantity: i.quantity,
                variantIndex:
                  i.variant_id &&
                  i.snapshot.variants.options.findIndex((o) => getDocId(o)?.toString() === i.variant_id.toString()),
                isAlwaysAvailable: i.snapshot.is_always_available,
              })),
            isCancelledOrder: true,
          })
          .toPromise();

        if (itemsResponse?.error) {
          session.endSession();
          throw new BadRequestException('Something went wrong');
        }
      }

      if (order.coupon_code !== undefined) {
        await this.brokerTransport
          .send<Coupon>(BROKER_PATTERNS.ITEM.TRACK_ORDER_COUPON, {
            order_id: order._id,
            coupon_code: order.coupon_code,
            store: (order.store as Store).id,
            shouldCancelOrder: true,
            customer: getDocId(order.customer),
          })
          .toPromise();
      }
    }

    session.commitTransaction();

    if (order?.meta?.chowbot?.session_id && order.channel === ORDER_CHANNELS.CHATBOT) {
      if ([ORDER_STATUSES.FULFILLED, ORDER_STATUSES.CANCELLED, ORDER_STATUSES.PROCESSING].includes(status)) {
        await this.brokerTransport
          .send(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_ORDER_STATUS_UPDATE, {
            session_id: order.meta.chowbot.session_id,
            status,
            storeId,
            reason,
            notifyCustomer: notifyCustomerViaBot,
            notifySeller: notifySellerViaBot,
            orderId: getDocId(order),
          })
          .toPromise();
      }

      // if (status === ORDER_STATUSES.PROCESSING) {
      //   await this.brokerTransport
      //     .send(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_ORDER_STATUS_UPDATE, {
      //       session_id: order.meta.chowbot.session_id,
      //       status: ORDER_STATUSES.PROCESSING,
      //       store: storeId,
      //     })
      //     .toPromise();
      // }

      // if (status === ORDER_STATUSES.CANCELLED) {
      //   await this.brokerTransport
      //     .send(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_ORDER_STATUS_UPDATE, {
      //       session_id: order.meta.chowbot.session_id,
      //       status: ORDER_STATUSES.CANCELLED,
      //       reason,
      //       storeId,
      //     })
      //     .toPromise();
      // }

      //todo: make an update to check if store has enabled confirm orders before payments, if not send a message to the customer for confirmed & fulfilled status
    }

    if (order.invoice) {
      const invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.UPDATE_FROM_ORDER, order)
        .toPromise();
    }

    // await this.sendCustomerStatusEmail(order, url);
    await this.sendCustomerStatusMessage(order, url, reason);

    // Check stock thresholds when order is fulfilled

    return order;
  }

  async cancelAndMarkAsUnpaid(storeId: string, orderIds: string[]) {
    const orders = await this.orderModel.find({ _id: { $in: orderIds }, store: storeId, is_deleted: { $ne: true } });
    if (!orders) {
      return;
    }

    await this.orderModel.updateMany(
      { _id: { $in: orderIds }, store: storeId, is_deleted: { $ne: true } },
      { status: ORDER_STATUSES.CANCELLED, is_paid: false, payment_id: null },
    );
  }

  async confirmOrder(storeId: string, orderId: string, url: string) {
    let order = await this.orderModel
      .findOne({
        _id: orderId,
        is_deleted: { $ne: true },
      })
      .populate('customer');

    checkIfUserOwnsStore(order.store as Store, storeId);

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: order.store })
      .toPromise();
    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (!order) {
      throw new BadRequestException('Order link has expired');
    }

    if (order.store != storeId) {
      throw new ForbiddenException('Only store owner can confirm order');
    }

    order.timeline.push({
      status: ORDER_STATUSES.PROCESSING,
      time: new Date(),
    });

    const session = await this.connection.startSession();
    session.startTransaction();

    let customer: Customer;

    if (await this.customerModel.findById(order.customer.id || order.customer._id)) {
      customer = await this.customerModel
        .findByIdAndUpdate(order.customer.id, order.customer, {
          new: true,
          session,
        })
        .exec();
    } else {
      customer = await new this.customerModel(order.customer).save({
        session,
      });
    }

    await order.update(
      {
        status: ORDER_STATUSES.PROCESSING,
        customer: customer._id,
      },
      { session },
    );

    if (order?.meta?.chowbot?.session_id && order.channel === ORDER_CHANNELS.CHATBOT) {
      await this.brokerTransport
        .emit(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_ORDER_STATUS_UPDATE, {
          session_id: order.meta.chowbot.session_id,
          status: ORDER_STATUSES.PROCESSING,
          storeId,
        })
        .toPromise();
    }

    await session.commitTransaction();

    await order.populate('customer').populate('store').execPopulate();

    // await this.sendCustomerStatusEmail(
    //   {
    //     ...order.toJSON(),
    //   },
    //   url,
    // );

    const orderJson = order.toJSON();
    await this.sendCustomerStatusMessage(orderJson, url);
    // await this.sendCustomerStatusEmail(orderJson, url);

    if (orderJson.invoice) {
      const invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.UPDATE_FROM_ORDER, orderJson)
        .toPromise();

      (orderJson as any).invoice = {
        status: invoice.status,
        invoiceId: invoice.invoice_id,
        payments_enabled: invoice?.payments_enabled,
        id: invoice.id,
      };
    }

    // await this.brokerTransport.send<Invoice>(BROKER_PATTERNS.INVOICE.UPDATE_FROM_ORDER, order.toJSON()).toPromise();

    return {
      order,
      customer,
    };
  }

  async addStatusToTimeLine(filter: FilterQuery<Order>, status: ORDER_STATUSES) {
    const order = await this.orderModel.findOne({ ...filter, is_deleted: { $ne: true } });

    if (!order) {
      throw new BadRequestException('Order does not exist');
    }

    order.timeline.push({
      status: status,
      time: new Date(),
    });

    //quick fix to make order confirmed after payments
    if (order.status === ORDER_STATUSES.PENDING) {
      order.timeline.push({
        status: ORDER_STATUSES.PROCESSING,
        time: new Date(),
      });

      order.status = ORDER_STATUSES.PENDING;
    }

    await order.save();

    return order.toJSON() as Order;
  }

  async addReceiptToOrder(filter: FilterQuery<Order>, receipt_id: string) {
    const order = await this.orderModel.findOne({ ...filter, is_deleted: { $ne: true } });

    if (!order) return;

    order.receipt = receipt_id;

    await order.save();

    return order.toJSON() as Order;
  }

  async updateOrder(filter: FilterQuery<Order>, payload: any) {
    const order = await this.orderModel.findOne({ ...filter, is_deleted: { $ne: true } });

    if (!order) return;

    const updatedOrder = await this.orderModel.findOneAndUpdate(filter, payload, { new: true });

    // order.delivery = delivery_id;

    // await order.save();

    return updatedOrder.toJSON() as Order;
  }

  async markOrderAsPaid(filter: FilterQuery<Order>, updateInvoice?: boolean, receipt?: string, paymentId?: string) {
    const order = await this.orderModel
      .findOne({ ...filter, is_deleted: { $ne: true } })
      .populate('customer')
      .populate('store');

    if (!order) {
      // throw new BadRequestException('Order does not exist');
      return { error: 'Order does not exist' };
    }

    if (order.is_paid) {
      // throw new BadRequestException('Order was previously marked as paid');
      return { error: 'Order was previously marked as paid' };
    }

    if (order.status === ORDER_STATUSES.PENDING && updateInvoice) {
      //updateInvoice true means the request is coming from the seller on the dashboard
      // throw new BadRequestException('Please confirm order before marking as paid');
      return { error: 'Please confirm order before marking as paid' };
    }

    if (order.status === ORDER_STATUSES.CANCELLED || (order.status === ORDER_STATUSES.ABANDONED && updateInvoice)) {
      // throw new BadRequestException(
      //   'Cannot mark a cancelled order as paid. If order is abandoned, please move to processing',
      // );
      return {
        error: 'Cannot mark a cancelled order as paid. If order is abandoned, please move to processing',
      };
    }

    let invoiceData;

    order.timeline = [
      ...order.timeline,
      {
        status: ORDER_STATUSES.PAYMENT_RECEIVED,
        time: new Date(),
      },
    ];
    order.is_paid = true;
    order.payment_id = paymentId;

    if (order.status === ORDER_STATUSES.ABANDONED) {
      order.status = ORDER_STATUSES.PENDING;

      try {
        await this.brokerTransport
          .send<{ error: boolean; items: any[] }>(BROKER_PATTERNS.ITEM.TRACK_ITEM_QUANTITIES, {
            items: order.items.map((i) => ({
              itemId: i.item_id,
              quantity: i.quantity,
              variantIndex:
                i.variant_id &&
                i.snapshot.variants.options.findIndex((o) => getDocId(o)?.toString() === i.variant_id.toString()),
              isAlwaysAvailable: i.snapshot?.is_always_available || !i.snapshot.quantity,
            })),
          })
          .toPromise();
      } catch (error) {
        this.logger.error(error);
      }
    }

    if (updateInvoice && order?.invoice) {
      //update the associated invoice
      const updatedInvoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.INVOICE_PAID, {
          id: order.invoice,
          payment_method: PAYMENT_METHODS.UNKNOWN,
          fromOrder: true,
        })
        .toPromise();

      invoiceData = {
        status: updatedInvoice.status,
        invoiceId: updatedInvoice.invoice_id,
        payments_enabled: updatedInvoice?.payments_enabled,
        id: updatedInvoice.id,
      };

      order.receipt = updatedInvoice?.receipt;
    }

    if (receipt) {
      order.receipt = receipt;
    }

    await order.save();
    const orderJson = order.toJSON();

    orderJson.invoice = invoiceData ?? order?.invoice;

    if (orderJson.affiliate) {
      const affiliate = await this.brokerTransport
        .send<Affiliate>(BROKER_PATTERNS.AFFILIATE.GET_AFFILIATE, {
          _id: orderJson.affiliate,
        })
        .toPromise();

      await this.brokerTransport
        .send(BROKER_PATTERNS.AFFILIATE.UPDATE_ORDERS, {
          affiliate_id: orderJson.affiliate,
          count: 1,
        })
        .toPromise();

      if (!affiliate.email) return orderJson;

      const store = order.store as Store;
      const customerFirstName = order.customer.name.split(' ')[0];
      const emailData: EmailOrderData & { status_text: string } = {
        name: customerFirstName,
        preview_text: `${customerFirstName} placed an order with your affiliate link`,
        store_name: store.name,
        store_color: store?.configuration.color,
        store_logo: store?.logo,
        status_text: order.status,
        store_phone: store.phone,
        is_affiliate: true,
        order: {
          order_id: order.id,
          link: `${process.env.CATLOG_WWW}/orders/${order.id}?forCustomer=true`,
          payment_link: `${process.env.CATLOG_WWW}/pay/${order.id}?byCustomer=true`,
          order_status: order.status,
          products: order.items.map(({ snapshot: item, quantity, variant }) => ({
            image: getProductItemThumbnail(item),
            name: item?.name,
            price: variant ? variant.price : item.price,
            quantity: quantity,
          })),
          total: order.total_amount,
          fees: order.fees.map((fee) => ({
            label: fee.label,
            amount: fee.amount,
          })),
          delivery_method: order.delivery_method,
          delivery_area: order?.delivery_info?.delivery_area?.name,
          delivery_address: order?.delivery_info?.delivery_address,
          currency: order.currency,
        },
      };

      const payload = {
        to: affiliate.email,
        subject: `New order from your (${store.name}) Affiliate Link 🎉`,
        data: emailData,
      };

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.AFFILIATE_ORDER, payload);
    }

    return orderJson;
  }

  /* NEW ORDER UPDATE SERVICES START */

  async updateOrderFromSeller(id: string, updateOrderDto: UpdateOrderFromSellerDto, storeId: string) {
    const order = await this.orderModel
      .findOne({ _id: id, is_deleted: { $ne: true } })
      .populate('store', STORE_PUBLIC_HIDDEN_INFO.replace('-subscription', ''))
      .populate('customer');
    const store = order.store as Store;
    checkIfUserOwnsStore(store, storeId);
    this.verifyOrderForUpdate(order);

    let fees = {
      couponCode: updateOrderDto.coupon_code,
      deliveryArea: updateOrderDto.delivery_area,
      discount: updateOrderDto.discount,
    };

    // if (order?.currency !== store?.currencies.products && !updateOrderDto?.rates) {
    //   throw new BadRequestException('Please provide a currency rates id');
    // }

    const exchangeRates = await this.currencyConversion.getExchangeRates(
      updateOrderDto?.rates,
      store?.currencies?.storefront,
      store?.currencies?.products,
    );

    if (!exchangeRates) throw new BadRequestException('Invalid currency rates id');
    const convertCurrency = (amount: number) =>
      this.currencyConversion.convertToCurrency(amount, order?.currency, exchangeRates, store?.currencies?.rates);

    const { resolvedItems, newFees, totalAmount, deliveryArea } = await this.resolveItemsAndFees(
      true,
      order,
      convertCurrency,
      updateOrderDto.items,
      fees,
      order?.delivery_method !== DELIVERY_METHODS.DELIVERY,
    );

    //remove current coupon code if it doesn't match previous coupon code, also add new coupon code
    if (updateOrderDto.coupon_code && updateOrderDto.coupon_code !== order?.coupon_code) {
      await this.brokerTransport
        .send<Coupon>(BROKER_PATTERNS.ITEM.TRACK_ORDER_COUPON, {
          order_id: order._id,
          coupon_code: order?.coupon_code,
          store: store.id,
          shouldCancelOrder: true,
          customer: getDocId(order.customer),
        })
        .toPromise();

      await this.brokerTransport
        .send<Coupon>(BROKER_PATTERNS.ITEM.TRACK_ORDER_COUPON, {
          order_id: order._id,
          coupon_code: updateOrderDto.coupon_code,
          store: store.id,
          customer: getDocId(order.customer),
        })
        .toPromise();
    }

    if (updateOrderDto.items) {
      const oldItemsMap = order.items.reduce<{ [key: string]: number }>((previous, current) => {
        previous[current.item_id] = current.quantity;
        return previous;
      }, {});

      await this.brokerTransport
        .send(BROKER_PATTERNS.ITEM.TRACK_ITEM_QUANTITIES, {
          items: resolvedItems.map((i) => ({
            itemId: i.item_id,
            quantity: i.quantity - (oldItemsMap[i.item_id] ?? 0),
            variantIndex:
              i.variant_id &&
              i.snapshot.variants.options.findIndex((o) => o?._id?.toString() === i.variant_id.toString()),
            isAlwaysAvailable: i.snapshot.is_always_available,
          })),
        })
        .toPromise();
    }

    const updatedOrder = await this.orderModel
      .findOneAndUpdate(
        { _id: id },
        {
          is_paid: updateOrderDto.isPaid || order.is_paid,
          items: resolvedItems,
          total_amount: totalAmount,
          fees: newFees,
          coupon_code: updateOrderDto.coupon_code ? updateOrderDto.coupon_code : order?.coupon_code,
          delivery_info:
            order?.delivery_method === DELIVERY_METHODS.PICKUP
              ? null
              : deliveryArea
              ? {
                  ...order?.delivery_info,
                  delivery_area: deliveryArea,
                }
              : order?.delivery_info,
        },
        { new: true, useFindAndModify: false },
      )
      .populate('store');

    let orderJson: Order = updatedOrder.toJSON();
    orderJson = await this.populateParsedOrder(orderJson);

    if (orderJson.invoice) {
      const invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.UPDATE_FROM_ORDER, orderJson)
        .toPromise();
      (orderJson as any).invoice = {
        status: invoice.status,
        invoiceId: invoice.invoice_id,
        payments_enabled: invoice?.payments_enabled,
        id: invoice.id,
      };
    }

    return { ...orderJson, customer: order.customer };
  }

  async updateOrderFromCustomer(id: string, updateOrderDto: UpdateOrderFromCustomerDto) {
    const order = await this.orderModel
      .findById(id)
      .populate('customer')
      .populate('store', STORE_PUBLIC_HIDDEN_INFO.replace('-subscription', ''));

    const store = order?.store as Store;

    this.verifyOrderForUpdate(order);

    if (order.customer.phone !== updateOrderDto.phone) {
      throw new UnauthorizedException("You're not authorized to update this order");
    }

    const exchangeRates = await this.currencyConversion.getExchangeRates(
      updateOrderDto?.rates,
      store?.currencies?.storefront,
      store?.currencies?.products,
    );
    if (!exchangeRates) throw new BadRequestException('Invalid currency rates id');
    const convertCurrency = (amount: number) =>
      this.currencyConversion.convertToCurrency(amount, order?.currency, exchangeRates, store?.currencies?.rates);

    const { resolvedItems, newFees, totalAmount, deliveryArea } = await this.resolveItemsAndFees(
      false,
      order,
      convertCurrency,
      updateOrderDto.items,
      { deliveryArea: updateOrderDto?.delivery_info?.area },
      (updateOrderDto?.delivery_method ?? order?.delivery_method) !== DELIVERY_METHODS.DELIVERY,
    );

    const deliveryUpdates = updateOrderDto?.delivery_info
      ? {
          delivery_info: {
            ...order.delivery_info,
            ...updateOrderDto?.delivery_info,
            deliveryArea,
          },
          delivery_address: updateOrderDto?.delivery_info?.delivery_address,
        }
      : undefined;

    if (updateOrderDto.items) {
      const oldItemsMap = order.items.reduce<{ [key: string]: number }>((previous, current) => {
        previous[current.item_id] = current.quantity;
        return previous;
      }, {});

      await this.brokerTransport
        .send(BROKER_PATTERNS.ITEM.TRACK_ITEM_QUANTITIES, {
          items: resolvedItems.map((i) => ({
            itemId: i.item_id,
            quantity: i.quantity ?? 0 - oldItemsMap[i.item_id] ?? 0,
            variantIndex:
              i.variant_id &&
              i.snapshot.variants.options.findIndex((o) => o?._id?.toString() === i.variant_id.toString()),
            isAlwaysAvailable: i.snapshot.is_always_available,
          })),
        })
        .toPromise();
    }

    const updatedOrder = await this.orderModel
      .findOneAndUpdate(
        { _id: id },
        {
          fees: newFees,
          total_amount: totalAmount,
          delivery_method: updateOrderDto?.delivery_method ?? order?.delivery_method,
          ...deliveryUpdates,
          items: resolvedItems,
        },
        { new: true, useFindAndModify: false },
      )
      .populate('store');

    let orderJson: Order = updatedOrder.toJSON();
    orderJson = await this.populateParsedOrder(orderJson);

    if (orderJson.invoice) {
      const invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.UPDATE_FROM_ORDER, orderJson)
        .toPromise();
      (orderJson as any).invoice = {
        status: invoice.status,
        invoiceId: invoice.invoice_id,
        payments_enabled: invoice?.payments_enabled,
        id: invoice.id,
      };
    }

    return { ...orderJson, customer: order.customer };
  }

  async countOrders(filter: FilterQuery<Order>) {
    return this.orderModel.countDocuments(filter);
  }

  verifyOrderForUpdate(order: Order) {
    if (!order || order.is_deleted) {
      throw new BadRequestException('Order does not exist');
    }

    if (order.status !== ORDER_STATUSES.PENDING) {
      throw new PreconditionFailedException('Only pending orders can be edited');
    }
  }

  async populateParsedOrder(order: any): Promise<Order> {
    let parsedOrderCopy = { ...order };
    parsedOrderCopy.items = await this.resolveItems(parsedOrderCopy.items, parsedOrderCopy.store as Store);

    parsedOrderCopy.store.delivery_areas = await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.GET_DELIVERY_AREAS, [...parsedOrderCopy?.store?.delivery_areas])
      .toPromise();

    (parsedOrderCopy.store as Store).country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: (parsedOrderCopy.store as Store).country,
      })
      .toPromise();

    return parsedOrderCopy;
  }

  async resolveDeliveryArea(area?: string) {
    let deliveryArea = undefined as DeliveryArea;
    if (area) {
      deliveryArea = await this.brokerTransport
        .send<DeliveryArea>(BROKER_PATTERNS.STORE.GET_DELIVERY_AREA, {
          _id: area,
        })
        .toPromise();
    }
    return deliveryArea;
  }

  async resolveItemsAndFees(
    fromSeller: boolean = true,
    order: Order,
    convertCurrency: (amount: number) => number,
    items?: any[],
    fees?: { couponCode?: string; deliveryArea?: string; discount?: number },
    isPickupDelivery: boolean = false,
  ) {
    let newDeliveryArea: DeliveryArea;
    const { couponCode, deliveryArea, discount } = fees;
    const { store, coupon_code: oldCouponCode, delivery_info, fees: orderFees } = order;

    const orderDiscount = orderFees.find((f) => f.type === FEE_TYPES.DISCOUNT)?.amount;

    if (deliveryArea) {
      newDeliveryArea = await this.brokerTransport
        .send<DeliveryArea>(BROKER_PATTERNS.STORE.GET_DELIVERY_AREA, {
          _id: deliveryArea,
        })
        .toPromise();
    }

    const resolvedItems = items ? await this.resolveItems(items, store as Store, true) : order.items;

    let totalAmount = this.calculateTotalOrderAmount(resolvedItems, convertCurrency); //convert currency here because fees will also be converted
    let couponAmount = 0;
    let coupon: Coupon = null;

    //sellers can remove coupon from order - and whenever the order update data is sent - it'll always include a coupon if one was applied
    //if no coupon was applied, do not calculate a discount
    //couponCode is from seller & oldCouponCode is previous coupon from order
    if ((fromSeller && couponCode) || (!fromSeller && oldCouponCode)) {
      coupon = await this.getCoupon(fromSeller ? couponCode : oldCouponCode, (store as Store)._id); // this only fetches the coupon from DB
      couponAmount = computeCouponDiscount(totalAmount, coupon, convertCurrency);
    }

    const computeDeliveryArea = () => {
      if (isPickupDelivery) return null;

      if (newDeliveryArea) return { ...newDeliveryArea, fee: convertCurrency(newDeliveryArea.fee) };

      return delivery_info?.delivery_area; //delivery_info comes from the old order details so no need to convert  currency
    };

    const computedFees = this.getFees(
      { coupon, amount: couponAmount },
      computeDeliveryArea(),
      convertCurrency,
      discount ? discount : orderDiscount ? Math.abs(orderDiscount) : null, //discount is expected to be in the correct currency so no need to convert currency
    );

    totalAmount = this.calculateTotalAmount(computedFees, totalAmount);

    return {
      totalAmount,
      newFees: computedFees,
      resolvedItems: this.currencyConversion.convertItemsToCurrency(resolvedItems, convertCurrency), //items aren't converted to currency yet
      deliveryArea: newDeliveryArea ?? null,
    };
  }

  async getCoupon(code: string, store: string) {
    return await this.brokerTransport
      .send<Coupon>(BROKER_PATTERNS.ITEM.GET_COUPON, {
        couponCode: code,
        user: { store: { id: store } },
      })
      .toPromise();
  }

  /* NEW ORDER UPDATE SERVICES END */

  async previewOrder(orderId: string, { token, phone }) {
    let verified;
    let storeId;
    const order = await this.orderModel
      .findOne({ _id: orderId, is_deleted: { $ne: true } })
      .populate('customer')
      .populate('store', STORE_PUBLIC_HIDDEN_INFO.replace('-subscription', ''));
    const store = order?.store as Store;

    const subscription = await this.brokerTransport
      .send(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, { _id: store?.subscription })
      .toPromise();

    if (!order) {
      throw new BadRequestException('Order link has expired');
    }

    let parsedOrder: Order = order.toJSON();

    if (phone) {
      verified = parsedOrder.customer.phone.replace('+', '') === phone;

      //request seems to be trimming the "+" off the phone number
    }

    if (!verified && token) {
      verified = await this.brokerTransport.send(BROKER_PATTERNS.USER.VERIFY_JWT_TOKEN, { token: token }).toPromise();

      if (verified) {
        storeId = verified.store.id;
      }
    }

    if (!verified) {
      throw new UnauthorizedException("You're not authorized to view the order");
    }

    if (storeId && String((parsedOrder.store as Store).id) !== storeId) {
      throw new ForbiddenException("Order isn't owned by store ");
    }

    parsedOrder = (await this.populateParsedOrder(parsedOrder)) as any;

    //remove subscription and add plan
    delete (parsedOrder.store as Store).subscription;
    (parsedOrder.store as any).plan = subscription?.plan?.type;

    if (parsedOrder?.invoice) {
      const invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, {
          order: parsedOrder.id,
        })
        .toPromise();

      (parsedOrder as any).invoice = {
        status: invoice.status,
        invoiceId: invoice.invoice_id,
        payments_enabled: invoice?.payments_enabled,
        id: invoice.id,
      };
    }

    if (store.currencies?.storefront.length > 1) {
      const rates = await this.brokerTransport
        .send<CurrencyRates>(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATES, {})
        .toPromise();
      (parsedOrder.store as any).meta = { rates } as any;
    }

    return parsedOrder;
  }

  async exportOrders(storeId, filter?: any) {
    const created_at = filter
      ? {
          $gte: new Date(filter.from),
          $lte: new Date(filter.to),
        }
      : undefined;

    const currency = filter?.currency ? { currency: filter.currency } : {};
    const status = filter?.status ? { status: filter.status } : {};

    const filterQuery = created_at
      ? {
          store: storeId,
          created_at,
          ...currency,
          ...status,
        }
      : { store: storeId, ...currency, ...status };

    const fields = {
      items: 1,
      created_at: 1,
      total_amount: 1,
      customer: 1,
      currency: 1,
      discount_amount: 1,
      delivery_address: 1,
      coupon_code: 1,
      status: 1,
      delivery_method: 1,
      fees: 1,
      channel: 1,
    };

    const orders = await this.orderModel
      .find({ ...filterQuery, is_deleted: { $ne: true } }, fields)
      .populate('customer')
      .lean();

    const ordersData = orders
      .map((o) => {
        const couponAmount = o?.coupon_code ? o.fees.find((f) => f.type === FEE_TYPES.COUPON)?.amount : '';

        return o.items.map((i) => {
          const unitPrice = i?.variant ? i?.variant?.price : i?.snapshot?.price;
          return {
            item: i.snapshot?.name,
            currency: o.currency,
            unit_price: amountFormat(unitPrice),
            quantity: i.quantity,
            total_price: amountFormat(unitPrice * i.quantity),
            status: o.status,
            channel: o?.channel?.split('_').join(' ') ?? 'STORE FRONT',
            delivery_method: o.delivery_method,
            customer_name: o?.customer?.name,
            customer_phone: o?.customer?.phone?.replace('-', ''),
            coupon_applied: o?.coupon_code,
            coupon_amount: couponAmount ? amountFormat(Math.abs(couponAmount)) : '',
            date: dayjs(o.created_at).format('dddd, MMMM D, YYYY h:mm A'),
          };
        });
      })
      .flat();

    const spreadSheetData = [
      {
        sheet: 'Orders',
        columns: Object.keys(ordersData[0]).map((f) => ({
          label: f.split('_').join(' ').toUpperCase(),
          value: f,
        })),
        content: [...ordersData],
      },
    ];

    return xlsx(spreadSheetData as any, {
      extraLength: 3,
      writeOptions: {
        type: 'buffer',
        bookType: 'xlsx',
      },
    });
  }

  async getOrderStats(storeId: string, filter?: any) {
    const created_at = filter
      ? {
          $gte: new Date(filter.from),
          $lt: new Date(filter.to),
        }
      : undefined;

    const filterQuery = created_at
      ? {
          store: storeId,
          created_at,
        }
      : { store: storeId };

    const orders = await this.orderModel
      .find(
        {
          ...filterQuery,
          is_deleted: { $ne: true },
          status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
        },
        { created_at: 1, total_amount: 1, customer: 1, currency: 1, status: 1, channel: 1 },
      )
      .lean();

    const currencies = [];
    const groupedData = {};
    const customers = {};
    const overallChannels = {};

    for (let index = 0; index < orders.length; index++) {
      const order = orders[index];
      const currency = order?.currency;

      if (!currencies.includes(currency)) currencies.push(currency);

      const currencyCustomers = customers[currency] ? [...customers[currency]] : [];
      const currencyData = groupedData[currency]
        ? { ...groupedData[currency] }
        : {
            totalOrders: 0,
            totalCustomers: 0,
            totalOrdersAmount: 0,
            totalCompletedOrders: 0,
            orders: {
              volumes: [],
              units: [],
            },
            channels: {},
          };

      if (order.status === ORDER_STATUSES.FULFILLED) {
        currencyData.totalCompletedOrders += 1;
      }

      if (!currencyCustomers.includes(String(order.customer))) {
        currencyData.totalCustomers += 1;
        currencyCustomers.push(String(order.customer));

        customers[currency] = currencyCustomers;
      }

      currencyData.totalOrdersAmount += order?.total_amount;
      currencyData.totalOrders += 1;

      currencyData.orders.units.push({ time: order.created_at, value: order._id });
      currencyData.orders.volumes.push({ time: order.created_at, value: order.total_amount });

      // Handle channel stats per currency
      const channel = order.channel || ORDER_CHANNELS.STORE_FRONT;
      if (!currencyData.channels[channel]) {
        currencyData.channels[channel] = { count: 0 };
      }
      currencyData.channels[channel].count += 1;

      // Handle overall channel stats
      if (!overallChannels[channel]) {
        overallChannels[channel] = { count: 0 };
      }
      overallChannels[channel].count += 1;

      groupedData[currency] = currencyData;
    }

    return {
      currencies,
      groupedData,
      channels: overallChannels,
    };
  }

  validateMinimumOrderQuantity(items: OrderItem[]) {
    const itemsWithInadequateQuantity = items.filter((item) => item.quantity < item.item?.minimum_order_quantity);
    if (itemsWithInadequateQuantity.length > 0)
      throw new BadRequestException(
        'Minimum order quantity not met for ' + arrayToSentence(itemsWithInadequateQuantity.map((i) => i.item.name)),
      );
  }

  calculateTotalOrderAmount(items: OrderItem[], toCurrency: (amount: number) => number) {
    return items.reduce((sum, item) => {
      let price = toCurrency((item as any).variant?.price || item.snapshot.price);
      sum += Number(price) * Number(item.quantity);
      return sum;
    }, 0);
  }

  getFees(
    couponDetails: { coupon: Coupon; amount: number },
    deliveryArea: DeliveryArea,
    convertCurrency: (amount: number) => number,
    discount?: number,
    vat?: number,
    deliveryFee?: number,
    paymentFee?: number,
    others?: { label: string; amount: number }[],
  ) {
    const fees: OrderFee[] = [];

    if (couponDetails?.coupon) {
      fees.push({
        label: `Coupon (${couponDetails.coupon.coupon_code})`,
        amount: couponDetails.amount,
        type: FEE_TYPES.COUPON,
      });
    }

    if (discount && discount > 0) {
      fees.push({
        label: `Discount`,
        amount: -1 * discount, //discount is expected to be in the correct currency so no need to convert currency
        type: FEE_TYPES.DISCOUNT,
      });
    }

    if (!deliveryFee && deliveryArea) {
      fees.push({
        label: `Delivery to ${deliveryArea.name}`,
        amount: convertCurrency(deliveryArea.fee),
        type: FEE_TYPES.DELIVERY,
      });
    }

    if (deliveryFee) {
      fees.push({
        label: `Delivery to ${deliveryArea ? deliveryArea.name : 'Customer'}`,
        amount: convertCurrency(deliveryFee),
        type: FEE_TYPES.DELIVERY,
      });
    }

    if (paymentFee) {
      fees.push({
        label: `Payment Fee`,
        amount: convertCurrency(paymentFee),
        type: FEE_TYPES.PAYMENT,
      });
    }

    if (vat && vat > 0) {
      fees.push({
        label: `VAT`,
        amount: convertCurrency(vat),
        type: FEE_TYPES.VAT,
      });
    }

    if (others && others.length > 0) {
      others.forEach((other) => {
        fees.push({
          label: other.label,
          amount: convertCurrency(other.amount),
          type: FEE_TYPES.OTHERS,
        });
      });
    }

    return fees;
  }

  calculateTotalAmount(fees: OrderFee[], currentTotal: number) {
    return fees.reduce((total, fee) => total + fee.amount, currentTotal);
  }

  async resolveItems(
    items: Order['items'],
    store: Store = null,
    computeDiscount: boolean = false,
    checkVariants: boolean = false,
  ) {
    let discounts =
      computeDiscount &&
      (await this.brokerTransport
        .send<DiscountDocument[]>(BROKER_PATTERNS.ITEM.GET_DISCOUNTS, {
          store: store?.id,
        })
        .toPromise());

    const discountMap = new Map<string, DiscountDocument>();

    if (discounts && discounts.length > 0) {
      for (let discount of discounts) {
        discountMap.set(discount.id, discount);
      }
    }

    return Promise.all(
      items.map(async (orderItem) => {
        let item = await this.brokerTransport
          .send<Item>(BROKER_PATTERNS.ITEM.GET_ITEM, {
            _id: orderItem.item_id,
          })
          .toPromise();

        // Skip the item if it's null
        if (!item) {
          return null; // Return null to indicate this item should be skipped
        }

        // Cache original prices before applying any discounts
        item.original_price = item.price;
        if (orderItem.snapshot) {
          orderItem.snapshot.original_price = item?.price;
        }

        // Cache variant prices if they exist
        if (orderItem.variant) {
          orderItem.variant.original_price = orderItem.variant.price;
        } else if (item.variants?.options?.length > 0) {
          const variantIndex = item.variants?.options?.findIndex(
            (v) => String(v._id || (v as any).id) === String(orderItem.variant_id),
          );
          if (variantIndex >= 0) {
            item.variants.options[variantIndex].original_price = item.variants.options[variantIndex].price;
          }
        }

        if (computeDiscount) {
          const discount = discountMap.get(item.discount?.toString());
          let discountPrice = 0;

          if (discount) {
            discountPrice = getDiscountPrice(item.price, discount);
          }

          if (discountPrice && discountPrice > 0) {
            item.price = discountPrice ?? item.price;
            orderItem.snapshot && (orderItem.snapshot.price = discountPrice ?? orderItem.snapshot.price);

            if (orderItem.variant) {
              const variantPrice = orderItem.variant.price;
              orderItem.variant.price = getDiscountPrice(variantPrice, discount) ?? variantPrice;
            } else {
              const variantIndex = item.variants?.options?.findIndex(
                (v) => String(v._id || (v as any).id) === String(orderItem.variant_id),
              );

              if (variantIndex >= 0) {
                const variantPrice = item.variants.options[variantIndex].price;
                item.variants.options[variantIndex].price = getDiscountPrice(variantPrice, discount) ?? variantPrice;
              }
            }
          }

          //COMPUTE DISCOUNT AMOUNT FOR VARIANT ITEMS WITHOUT a discount data
          else if (item?.discount_price && item?.variants?.options.length > 0) {
            const discountAmount = item.price - item.discount_price;

            item.price = item.discount_price;

            if (orderItem.variant) {
              orderItem.variant.price = orderItem.variant.price - discountAmount;
            } else {
              const variantIndex = item.variants?.options?.findIndex(
                (v) => String(v._id || (v as any).id) === String(orderItem.variant_id),
              );

              if (variantIndex >= 0) {
                const variantPrice = item.variants.options[variantIndex].price;
                item.variants.options[variantIndex].price = variantPrice - discountAmount;
              }
            }
          } else if (item?.discount_price && item?.variants?.options.length == 0) {
            item.price = item.discount_price;
          }
          delete item.discount;
        }

        // Apply tiered pricing if available and active
        const tieredPricing = item?.tiered_pricing as TieredPricing;
        if (tieredPricing && typeof tieredPricing === 'object' && tieredPricing.active) {
          // Use only orderItem.quantity for tiered pricing calculations
          // This is the actual quantity selected by the customer, not available stock quantities
          const selectedQuantity = orderItem.quantity;

          // Find the applicable tier based on the selected quantity
          const applicableTier = tieredPricing.tiers
            .sort((a, b) => a.minimum_quantity - b.minimum_quantity)
            .find((tier) => selectedQuantity >= tier.minimum_quantity);

          if (applicableTier && applicableTier.discount_percentage > 0) {
            const discountMultiplier = 1 - applicableTier.discount_percentage / 100;

            // Apply tiered pricing discount to the base price
            item.price = item.price * discountMultiplier;

            // Update snapshot price if it exists
            if (orderItem.snapshot) {
              orderItem.snapshot.price = item.price;
            }

            // Apply to variant if it exists
            if (orderItem.variant) {
              orderItem.variant.price = orderItem.variant.price * discountMultiplier;
            } else if (item.variants?.options?.length > 0) {
              const variantIndex = item.variants?.options?.findIndex(
                (v) => String(v._id || (v as any).id) === String(orderItem.variant_id),
              );

              if (variantIndex >= 0) {
                item.variants.options[variantIndex].price =
                  item.variants.options[variantIndex].price * discountMultiplier;
              }
            }
          }
        }

        if (store) {
          [item] = resolveCategories([item] as any, store);
        }

        if (checkVariants && (item.variants?.options?.length || 0) > 0 && !orderItem.variant_id) {
          throw new BadRequestException('Please select a variant for ' + item.name);
        }

        return {
          ...orderItem,
          snapshot: orderItem.snapshot || item,
          item: orderItem.snapshot || item,
          quantity: orderItem.quantity,
          item_id: orderItem.item_id,
          variant:
            orderItem.variant ||
            item.variants?.options?.find((v) => String(v._id || (v as any).id) === String(orderItem.variant_id)),
        };
      }),
    );
  }

  async generateInvoiceFromOrder(order_id: string, storeId: string) {
    const order = await this.orderModel.findById(order_id).populate('customer');

    if (!order || order.is_deleted) {
      throw new NotFoundException('Order not found');
    }

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: order.store })
      .toPromise();

    checkIfUserOwnsStore(store, storeId);

    paymentsEnabledGuard(store as Store);

    if (order.invoice) {
      throw new BadRequestException('Invoice already exists');
    }

    if (store?.payments_enabled) {
      const invoice = await this.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.FROM_ORDER, {
          ...order.toJSON(),
        })
        .toPromise();

      order.invoice = invoice?.invoice_id;
      order.save();
    }

    return order;
  }

  async generateOrderID() {
    let id = 'CLGO-' + genChars(8, false, true);
    while (await this.orderModel.exists({ _id: id })) {
      id = 'CLGO-' + genChars(8, false, true);
    }

    return id;
  }
  /*
  async sendCustomerStatusEmail(order: Order & { store: any }, url: string) {
    if (!order?.customer?.email) {
      return;
    }

    const customerFirstName = order.customer.name.split(' ')[0];

    if (order.status === ORDER_STATUSES.PROCESSING) {
      this.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_PROCESSING, {
        to: order.customer.email,
        subject: `Your order with ${order.store.name}, is now processing 🛍️`,
        data: {
          name: customerFirstName,
          order_id: order.id,
          store_name: order.store.name,
          store_phone: formatPhoneNumber(order.store.phone),
          order_link: `${process.env.CATLOG_WWW}/orders/${order.id}?forCustomer=true`,
        },
      });
    } else {
      const statusConfig = ORDER_STATUSES_CONFIG[order.status];
      this.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_STATUS, {
        to: order.customer.email,
        subject: `Your order with ${order.store.name}, ${statusConfig.status_text} ${statusConfig.status} 🛍️`,
        data: {
          name: customerFirstName,
          order_id: order.id,
          store_name: order.store.name,
          store_phone: formatPhoneNumber(order.store.phone),
          status: statusConfig.status,
          status_text: statusConfig.status_text,
          order_link: `${process.env.CATLOG_WWW}/orders/${order.id}?forCustomer=true`,
        },
      });
    }
  } */

  async sendCustomerStatusMessage(order: Order & { store: any }, url: string, reason?: string) {
    const shouldSendEmail = Boolean(order?.customer?.email);
    if (shouldSendEmail == false) return;

    const statusTexts = {
      [ORDER_STATUSES.PENDING]: 'is now',
      [ORDER_STATUSES.PROCESSING]: 'is now',
      [ORDER_STATUSES.FULFILLED]: 'has been',
      [ORDER_STATUSES.CANCELLED]: 'has been',
    };

    const customerFirstName = order.customer.name.split(' ')[0];
    const store = order.store;
    const paymentValidatesOrder = store?.configuration.payment_validates_order;
    // const storeCurrency = store.currencies.default;

    const emailData: EmailOrderData & { status_text: string } = {
      name: customerFirstName,
      preview_text: `Your order ${statusTexts[order.status]} ${order.status.toLocaleLowerCase()}`,
      store_name: store.name,
      store_color: store?.configuration.color,
      store_logo: store?.logo,
      status_text: statusTexts[order.status],
      store_phone: store.phone,
      pending_payment: !order.payment_id,
      order: {
        order_id: order.id,
        link: `${process.env.CATLOG_WWW}/orders/${order.id}?forCustomer=true`,
        payment_link: `${process.env.CATLOG_WWW}/pay/${order.id}?byCustomer=true`,
        order_status: order.status,
        products: order.items.map(({ snapshot: item, quantity, variant }) => ({
          image: getProductItemThumbnail(item),
          name: item?.name,
          price: variant ? variant.price : item.price,
          quantity: quantity,
        })),
        total: order.total_amount,
        fees: order.fees.map((fee) => ({
          label: fee.label,
          amount: fee.amount,
        })),
        delivery_method: order.delivery_method,
        delivery_area: order?.delivery_info?.delivery_area?.name,
        delivery_address: order?.delivery_info?.delivery_address,
        currency: order.currency,
      },
    };

    const payload = {
      to: order.customer.email,
      subject: '',
      data: emailData,
    };

    switch (order.status) {
      case ORDER_STATUSES.PENDING:
        if (!paymentValidatesOrder) {
          payload.subject = `Your order has been received [Pending Payment] 🎉`;
          await this.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_RECEIVED_CUSTOMER, payload);
        }
        break;
      case ORDER_STATUSES.PROCESSING:
        payload.subject = `Your order with ${order.store.name}, is now processing 🛍️`;
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_STATUS, payload);
        break;
      case ORDER_STATUSES.FULFILLED:
        payload.subject = `Your order with ${order.store.name}, has been fulfilled 🛍️`;
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_STATUS, payload);
        break;
      case ORDER_STATUSES.CANCELLED:
        payload.subject = `Your order with ${order.store.name}, has been cancelled ❌`;
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_STATUS, payload);
        break;
    }
  }

  async migrateOrderDeliveryToDeliveryInfo() {
    const orders = await this.orderModel.find({}).populate('customer');

    for (let order of orders) {
      const orderJson = order.toJSON();
      const { delivery_address, customer, delivery_info } = orderJson;

      order.delivery_info = {
        ...delivery_info,
        delivery_address: delivery_info?.delivery_address ?? delivery_address ?? '',
        name: delivery_info?.name ?? customer?.name ?? '',
        phone: delivery_info?.phone ?? customer?.phone ?? '',
      };

      if ((order.status as string) == 'CONFIRMED') {
        order.status = ORDER_STATUSES.PROCESSING;
      }
      await order.save();
    }
  }

  async migrateOrderCurrencies() {
    const orders = await this.orderModel
      .find({ currency: { $exists: false } }, { store: 1 })
      .limit(2500)
      .populate('store', 'country')
      .lean();

    const updates: Promise<OrderDocument>[] = [];

    for (let order of orders) {
      updates.push(
        this.orderModel
          .findByIdAndUpdate(order._id, {
            currency: COUNTRY_CURRENCY_MAP[(order?.store as Store)?.country as COUNTRY_CODE],
          })
          .exec(),
      );
    }

    await Promise.all(updates);

    return updates;
  }

  async migrateCustomerPhoneToFormatted() {
    const customers = await this.customerModel.find({});
    for (let i = 0; i < customers.length; i++) {
      let formattedPhone = formatPhoneNumber(customers[i].phone);
      customers[i].formatted_phone = formattedPhone;
      await customers[i].save();
    }
  }

  async migrateTotalOrdersForItems() {
    const batchSize = 2000;
    const delayBetweenBatches = 5000; // 5 seconds delay

    let offset = 0;
    let orders = await this.fetchBatchOfOrders(batchSize, offset);

    while (orders.length > 0) {
      const itemUpdates = this.calculateItemUpdates(orders);

      await this.updateItems(itemUpdates);

      await this.delay(delayBetweenBatches);

      offset += batchSize;
      orders = await this.fetchBatchOfOrders(batchSize, offset);
    }
  }

  private async fetchBatchOfOrders(batchSize: number, offset: number): Promise<OrderDocument[]> {
    return this.orderModel
      .find({ status: { $ne: ORDER_STATUSES.CANCELLED }, is_deleted: { $ne: true } })
      .skip(offset)
      .limit(batchSize)
      .exec();
  }

  private calculateItemUpdates(orders: OrderDocument[]): { [itemId: string]: number } {
    return orders.reduce((updates, order) => {
      order.items.forEach((item) => {
        if (!updates[item.item_id]) {
          updates[item.item_id] = item.quantity;
        } else {
          updates[item.item_id] += item.quantity;
        }
      });
      return updates;
    }, {});
  }

  private async updateItems(itemUpdates: { [itemId: string]: number }): Promise<void> {
    const updatePromises = Object.entries(itemUpdates).map(([itemId, totalOrders]) => {
      return this.brokerTransport
        .send<any>(BROKER_PATTERNS.ITEM.ADD_TOTAL_ORDERS, { filter: { _id: itemId }, total_orders: totalOrders })
        .toPromise();
    });

    await Promise.all(updatePromises);
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  removeStorePrivateInfo(store: Store) {
    delete store.invites;
    delete store.owners;
    delete store.slugs;
    delete store.disabled_slug;
    delete store.disabled_slugs;
    delete store.subscription;
    delete store.onboarding_steps;
    delete store.kyc;
    delete store.wallet;
    delete store.wallets;
    delete store.access_tokens;
    delete store.third_party_configs;
    delete store.total_visits;
    delete store.payment_options;
    delete store.meta;
    delete store.security_pin;
    delete store.milestones;

    return store;
  }
  // convertToCurrency(amount: number, currency: CURRENCIES, rates: { [key: string]: number }, markups: { [key: string]: number }) {
  //   const conversionRate = rates && rates[currency] !== undefined ? rates[currency] : 1;

  //   return Math.ceil(amount * conversionRate * 100) / 100; //format to two decimal places
  // }

  validateCoupon(coupon: Coupon) {
    return (
      coupon !== undefined &&
      coupon !== null &&
      coupon?.quantity !== 0 &&
      new Date(coupon?.end_date) >= new Date() &&
      coupon?.active !== false
    );
  }

  async updateOrderItemImageUrlToNewS3Url(): Promise<void> {
    const oldBaseUrl = 'https://catlog-1.s3.eu-west-2.amazonaws.com';
    const newBaseUrl = 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS';

    try {
      const batchSize = 2000;
      let totalUpdated = 0;

      // Filter to find only orders that contain the oldBaseUrl in snapshots
      const filter = {
        // 'items.snapshot': { $exists: true },
        $or: [
          { 'items.snapshot.images': { $elemMatch: { $regex: oldBaseUrl } } },
          { 'items.variant.image': { $regex: oldBaseUrl } },
          { 'items.snapshot.variants.options.image': { $regex: oldBaseUrl } },
        ],
      };

      let hasMoreDocuments = true;

      while (hasMoreDocuments) {
        const orders = await this.orderModel.find(filter).limit(batchSize).exec();

        if (orders.length === 0) {
          hasMoreDocuments = false;
          break;
        }

        const bulkOps = orders
          .map((order) => {
            let updated = false;

            if (Array.isArray(order.items)) {
              order.items.forEach((orderItem) => {
                if (orderItem.snapshot) {
                  const snapshot = orderItem.snapshot;

                  // Update snapshot.images
                  if (Array.isArray(snapshot.images)) {
                    const newImages = snapshot.images.map((imageUrl) => {
                      if (typeof imageUrl === 'string' && imageUrl.includes(oldBaseUrl)) {
                        updated = true;
                        return imageUrl.replace(oldBaseUrl, newBaseUrl);
                      }
                      return imageUrl;
                    });
                    snapshot.images = newImages;
                  }

                  // Update snapshot.variants.options[].image
                  if (snapshot.variants && Array.isArray(snapshot.variants.options)) {
                    const newOptions = snapshot.variants.options.map((option) => {
                      if (typeof option.image === 'string' && option.image.includes(oldBaseUrl)) {
                        updated = true;
                        option.image = option.image.replace(oldBaseUrl, newBaseUrl);
                      }
                      return option;
                    });
                    snapshot.variants.options = newOptions;
                  }
                }
                if (orderItem.variant) {
                  // Update orderItem.variant.image
                  if (typeof orderItem.variant.image === 'string' && orderItem.variant.image.includes(oldBaseUrl)) {
                    orderItem.variant.image = orderItem.variant.image.replace(oldBaseUrl, newBaseUrl);
                    updated = true;
                  }
                }
              });
            }

            // Prepare bulk operation if order was updated
            if (updated) {
              return {
                updateOne: {
                  filter: { _id: order._id },
                  update: {
                    $set: {
                      items: order.items,
                    },
                  },
                },
              };
            }
            return null;
          })
          .filter((op) => op !== null);

        if (bulkOps.length > 0) {
          const result = await this.orderModel.bulkWrite(bulkOps);
          totalUpdated += result.modifiedCount || 0;
          this.logger.log(`Batch updated ${result.modifiedCount || 0} orders.`);
        } else {
          // No bulk operations were performed, meaning no documents matched the update criteria
          hasMoreDocuments = false;
        }
      }

      this.logger.log(`Order image URLs updated successfully. Total updated: ${totalUpdated}.`);
    } catch (error) {
      this.logger.error('Error updating order image URLs:', error);
      throw error;
    }
  }

  async ordersWrapData(storeId: string, startDate: Date, endDate: Date): Promise<OrdersWrapData> {
    startDate = dayjs(startDate).toDate();
    endDate = dayjs(endDate).toDate();
    const storeObjectId = new mongoose.Types.ObjectId(storeId);
    // Step 1: Filter orders for the given date range and aggregate top 5 products
    const products = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.item_id',
          totalQuantity: { $sum: '$items.quantity' },
          name: { $first: '$items.snapshot.name' },
          thumbnail: { $first: '$items.snapshot.thumbnail' },
        },
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: 5 },
    ]);

    const topProduct = products[0]
      ? {
          id: products[0]._id,
          name: products[0].name,
          thumbnail: products[0].thumbnail,
          orders_count: products[0].totalQuantity,
        }
      : { id: '', name: '', thumbnail: '', orders_count: 0 };

    const otherTopProducts = products.slice(1).map((product) => ({
      id: product._id,
      name: product.name,
      thumbnail: product.thumbnail,
      orders_count: product.totalQuantity,
    }));

    // Step 2: Aggregate top 5 customers by order count with $lookup for customer data (name and phone)
    const customers = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      { $group: { _id: '$customer', ordersCount: { $sum: 1 } } },
      { $sort: { ordersCount: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: 'customers',
          localField: '_id',
          foreignField: '_id',
          as: 'customerInfo',
        },
      },
      { $unwind: { path: '$customerInfo', preserveNullAndEmptyArrays: true } },
    ]);

    // Extract top customer (first one in the list) and other top customers
    const topCustomer = customers[0]
      ? {
          id: customers[0]._id,
          name: customers[0].customerInfo?.name || '',
          phone: customers[0].customerInfo?.phone || '',
          orders_count: customers[0].ordersCount,
        }
      : { id: '', name: '', phone: '', orders_count: 0 };

    const otherTopCustomers = customers.slice(1).map((customerData) => ({
      id: customerData._id,
      name: customerData.customerInfo?.name || '',
      phone: customerData.customerInfo?.phone || '',
      orders_count: customerData.ordersCount,
    }));

    // Step 3: Aggregate the total number of distinct customers who placed orders
    const totalCustomersData = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: { _id: '$customer' },
      },
      {
        $count: 'totalCustomers', // Counts distinct customers
      },
    ]);

    const repeatCustomersData = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$customer',
          orderCount: { $sum: 1 },
        },
      },
      {
        $match: {
          orderCount: { $gt: 1 },
        },
      },
      {
        $count: 'repeatCustomers',
      },
    ]);

    const totalCustomers = totalCustomersData[0]?.totalCustomers || 0;
    const repeatCustomers = repeatCustomersData[0]?.repeatCustomers || 0;

    // Step 4: Get top order location (top city based on order count)
    const topOrdersLocationData = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
          created_at: { $gte: startDate, $lte: endDate },
          validated_delivery_address: { $ne: null },
        },
      },
      {
        $lookup: {
          from: 'addresses', // Ensure this matches the collection name for Address
          localField: 'validated_delivery_address',
          foreignField: '_id',
          as: 'address',
        },
      },
      { $unwind: '$address' },
      {
        $group: {
          _id: '$address.city',
          orderCount: { $sum: 1 },
        },
      },
      { $sort: { orderCount: -1 } },
      { $limit: 1 },
    ]);

    const topOrdersLocation = topOrdersLocationData[0] ? topOrdersLocationData[0]._id : '';
    const noOfOrdersFromTopLocation = topOrdersLocationData[0] ? topOrdersLocationData[0].orderCount : 0;

    // Step 5: Get month with highest orders
    const monthOrders = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $project: {
          month: { $month: '$created_at' },
        },
      },
      {
        $group: {
          _id: '$month',
          orderCount: { $sum: 1 },
        },
      },
      { $sort: { orderCount: -1 } },
      { $limit: 1 },
    ]);

    const monthWithHighestOrders = monthOrders[0] ? monthOrders[0]._id : 1; // Default to January if no orders

    // Step 6: Calculate total orders, paid orders, and total payment volume
    const noOfOrders = await this.orderModel
      .countDocuments({
        store: storeId,
        status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
        created_at: { $gte: startDate, $lte: endDate },
      })
      .exec();
    const paidOrders = await this.orderModel
      .countDocuments({ store: storeId, is_paid: true, created_at: { $gte: startDate, $lte: endDate } })
      .exec();
    const totalPaymentVolume = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          status: { $nin: [ORDER_STATUSES.ABANDONED, ORDER_STATUSES.CANCELLED] },
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      { $group: { _id: null, totalAmount: { $sum: '$total_amount' } } },
    ]);

    // Return the wrapped data
    return {
      topProduct,
      topProductOrders: products[0]?.totalQuantity || 0,
      otherTopProducts,
      topCustomer,
      otherTopCustomers,
      totalCustomers,
      repeatCustomers,
      topOrdersLocation,
      noOfOrdersFromTopLocation,
      monthWithHighestOrders,
      totalOrderCount: noOfOrders,
      paidOrderCount: paidOrders,
      totalOrderVolume: totalPaymentVolume[0]?.totalAmount ?? 0,
    };
  }

  async validatedDeliveryAddressMigration() {
    const batchSize = 2000;
    let skip = 0;
    let totalUpdated = 0;

    this.logger.log('Migration started.');

    const storesWithPayments = await this.paymentGetterService.getStoresWithPayments(
      dayjs('2024-01-01').toDate(),
      dayjs('2024-12-31').toDate(),
    );

    while (true) {
      // Fetch orders without validated delivery address in batches
      const orders = await this.orderModel
        .find({
          $or: [{ validated_delivery_address: { $exists: false } }, { validated_delivery_address: null }],
          created_at: { $gte: dayjs('2024-01-01').toDate() },
          store: { $in: storesWithPayments },
        })
        .skip(skip)
        .sort({ created_at: -1 })
        .limit(batchSize)
        .populate('customer') // Populate the customer to get name, phone, and email
        .exec();

      if (orders.length === 0) {
        this.logger.log('No more orders to process. Migration complete.');
        break; // If no more orders are found, stop processing
      }

      this.logger.log(`Processing batch: ${skip / batchSize + 1}`);
      this.logger.log(`Found ${orders.length} orders without validated delivery address.`);

      // Process each order
      const updatePromises = orders.map(async (order) => {
        // Skip orders that don't have delivery_info or delivery_address
        if (!order.delivery_info || !order.delivery_info.delivery_address) {
          this.logger.log(`Skipping order ${order._id}: No delivery info or address.`);
          return;
        }
        const deliveryInfo = order.delivery_info;

        if (deliveryInfo.delivery_address) {
          // Prepare CreateAddressDto
          const createAddressDto = {
            name: order.customer?.name || '',
            phone: order.customer?.phone || '',
            email: order.customer?.email || '<EMAIL>',
            address: deliveryInfo.delivery_address,
            customer: order.customer?._id.toString(),
          };

          try {
            // Create or fetch the validated address
            const validatedAddress = await this.brokerTransport
              .send<Address>(BROKER_PATTERNS.DELVERIES.CREATE_ADDRESS_USING_GOOGLE_VALIDATION, {
                store: order.store,
                data: createAddressDto,
              })
              .toPromise();

            // Update the order with validated delivery address ID
            const validated_delivery_address = validatedAddress && validatedAddress.id;
            try {
              // Attempt to save the order
              this.logger.log(`Saving order ${order._id}`);
              await this.orderModel.updateOne({ _id: order._id }, { $set: { validated_delivery_address } });
              totalUpdated++; // Increment the count of updated orders
            } catch (saveError) {
              this.logger.error(`Error saving order ${order._id}: ${saveError.message}`);
            }
          } catch (e) {
            this.logger.error(`Error updating order ${order._id}: ${e.message}`);
          }
        }
      });

      // Wait for all promises to resolve before continuing to the next batch
      await Promise.all(updatePromises);

      // Log the completion of this batch
      this.logger.log(`Batch ${skip / batchSize + 1} processed. ${totalUpdated} orders updated so far.`);

      // Increase skip for the next batch
      skip += batchSize;
    }

    this.logger.log(`Migration complete. Total orders updated: ${totalUpdated}`);
  }

  async getTopProducts(storeId: string, startDate: Date, endDate: Date, limit: number = 5) {
    const storeObjectId = new mongoose.Types.ObjectId(storeId);

    const products = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.item_id',
          totalQuantity: { $sum: '$items.quantity' },
          name: { $first: '$items.snapshot.name' },
          thumbnail: { $first: '$items.snapshot.thumbnail' },
        },
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: limit },
    ]);

    return products.map((product) => ({
      id: product._id,
      name: product.name || 'Unknown Product',
      thumbnail: product.thumbnail || '',
      orders_count: product.totalQuantity,
    }));
  }

  async getTopCustomers(storeId: string, startDate: Date, endDate: Date, limit: number = 3) {
    const storeObjectId = new mongoose.Types.ObjectId(storeId);

    const customers = await this.orderModel.aggregate([
      {
        $match: {
          store: storeObjectId,
          created_at: { $gte: startDate, $lte: endDate },
        },
      },
      { $group: { _id: '$customer', ordersCount: { $sum: 1 } } },
      { $sort: { ordersCount: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: 'customers',
          localField: '_id',
          foreignField: '_id',
          as: 'customerInfo',
        },
      },
      { $unwind: { path: '$customerInfo', preserveNullAndEmptyArrays: true } },
    ]);

    // Handle cases where customerInfo might be missing
    return customers.map((customer) => ({
      id: customer._id,
      name: customer.customerInfo?.name || 'Unknown Customer',
      phone: customer.customerInfo?.phone || '',
      orders_count: customer.ordersCount,
    }));
  }

  async remove(id: string, storeId: string) {
    const order = await this.orderModel.findOne({ _id: id, store: storeId, is_deleted: { $ne: true } });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Check if order is already fulfilled or paid
    if (order.status === ORDER_STATUSES.FULFILLED) {
      throw new BadRequestException('Cannot delete an order that has been fulfilled');
    }

    if (order.is_paid) {
      throw new BadRequestException('Cannot delete an order that has been paid');
    }

    // Add product quantities back to inventory if the order is not abandoned or cancelled
    if (order.status !== ORDER_STATUSES.ABANDONED && order.status !== ORDER_STATUSES.CANCELLED) {
      // Prepare items data for tracking quantities

      const itemsData = order.items
        .filter((i) => i.snapshot?.quantity > -1)
        .map((i) => ({
          itemId: i.item_id,
          quantity: i.quantity,
          variantIndex:
            i.variant_id &&
            i.snapshot.variants.options.findIndex((o) => getDocId(o)?.toString() === i.variant_id.toString()),
          isAlwaysAvailable: i.snapshot.is_always_available,
        }));

      // Call the item service to track quantities (add them back to inventory)
      await this.brokerTransport
        .send<{ error: boolean; items: any[] }>(BROKER_PATTERNS.ITEM.TRACK_ITEM_QUANTITIES, {
          items: itemsData,
          isCancelledOrder: true, // This flag tells the service to add quantities back
        })
        .toPromise();
    }

    // Soft delete the order by setting is_deleted to true
    order.is_deleted = true;
    await order.save();

    return {
      message: 'Order deleted successfully',
      data: order,
    };
  }

  async bulkMarkOrdersAsPaid(storeId: string, orderIds: string[]) {
    const logger = new Logger('OrdersService.bulkMarkOrdersAsPaid');
    const startTime = Date.now();

    const results = {
      updated_count: 0,
      failed_count: 0,
      updated_orders: [] as string[],
      failed_orders: [] as Array<{
        id: string;
        reason: string;
        current_status?: ORDER_STATUSES;
        is_paid?: boolean;
      }>,
    };

    try {
      // Fetch all orders in one query for efficiency
      const orders = await this.orderModel
        .find({
          _id: { $in: orderIds },
          store: storeId,
          is_deleted: { $ne: true },
        })
        .lean();

      // Create a map for quick lookup
      const orderMap = new Map(orders.map((order) => [order._id.toString(), order]));

      // Process each order
      for (const orderId of orderIds) {
        try {
          const order = orderMap.get(orderId);

          if (!order) {
            results.failed_orders.push({
              id: orderId,
              reason: 'Order not found or does not belong to this store',
            });
            results.failed_count++;
            continue;
          }

          // Use the existing markOrderAsPaid method to maintain consistency
          const result = await this.markOrderAsPaid(
            { _id: orderId, store: storeId },
            true, // updateInvoice = true for seller dashboard requests
          );

          if (result.error) {
            results.failed_orders.push({
              id: orderId,
              reason: result.error,
              current_status: order.status,
              is_paid: order.is_paid,
            });
            results.failed_count++;
          } else {
            results.updated_orders.push(orderId);
            results.updated_count++;
            logger.log(`Marked order ${orderId} as paid`);
          }
        } catch (error) {
          logger.error(`Error marking order ${orderId} as paid:`, error);
          results.failed_orders.push({
            id: orderId,
            reason: `Update failed: ${error.message}`,
          });
          results.failed_count++;
        }
      }

      const duration = Date.now() - startTime;
      logger.log(
        `Bulk mark as paid completed in ${duration}ms. Updated: ${results.updated_count}, Failed: ${results.failed_count}`,
      );

      return {
        ...results,
        message: `Bulk mark as paid completed. ${results.updated_count} orders marked as paid, ${results.failed_count} failed.`,
      };
    } catch (error) {
      logger.error('Error in bulk mark as paid:', error);
      throw new BadRequestException(`Bulk mark as paid failed: ${error.message}`);
    }
  }
}
