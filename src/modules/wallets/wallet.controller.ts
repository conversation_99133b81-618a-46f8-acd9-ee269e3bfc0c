import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  InternalServerErrorException,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import {
  CompleteWithdrawalRequestDto,
  CreatePaymentStatementDto,
  CreateWithdrawalAccountDto,
  DeleteWithdrawalAccountDto,
  ResolveAccountDto,
  WithdrawalRequestDto,
} from '../../models/dtos/wallet.dto';
import { PaystackRepository } from '../../repositories/paystack.repository';
import { WalletService } from './wallet.service';
import { IRequest, IResponse } from '../../interfaces/request.interface';
import {
  ManuallyCreditWalletDto,
  PaginatedQueryDto,
  ProcessPartialRefundDto,
  ProcessRefundDto,
  TriggerMissingSquadWebhooksDto,
  ValidateWithdrawalsDto,
} from './dtos/search.dto';
import logos from '../../utils/bank-logos';
import { CountryPermissons, RolePermissions } from '../../decorators/permission.decorator';
import { SCOPES } from '../../utils/permissions.util';
import { CountryGuard } from '../../guards/country.guard';
import { RoleGuard } from '../../guards/role.guard';
import { checkIfUserOwnsStore, paymentsEnabledGuard } from '../../utils';
import { Store } from '../store/store.schema';
import { fees, chowbotFees, minWithdrawalAmount, maxWithdrawalAmount } from '../../utils/withdrawal-config';
import {
  ApiExcludeEndpoint,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { CURRENCIES } from '../country/country.schema';
import { Readable } from 'stream';
import { WalletResolutionsService } from './resolutions.service';
import { CreateInternationalWalletRequestDto } from './dtos/wallet-request.dto';
import { UpdateWalletRequestDto } from './dtos/update-wallet-request.dto';
import { WALLET_REQUEST_STATUS } from './wallet.schema';
import { PAYMENT_FEE_PROFILES } from '../../utils/constants';
import { CustomerIoRepository } from '../../repositories/customer-io.repository';

@Controller('wallets')
// @UseGuards(JwtAuthGuard, CountryGuard)
export class WalletController {
  constructor(
    private readonly paystack: PaystackRepository,
    private readonly walletService: WalletService,
    private readonly walletResolutionsService: WalletResolutionsService,
    private readonly customerIo: CustomerIoRepository,
  ) {}

  @Get('')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.VIEW_WALLET)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getWallet(@Req() req: IRequest) {
    const userRole = req?.user?.role;
    const data = await this.walletService.getWalletFromStore(req.user.store.id, userRole);

    if (data && data?.account) {
      data.account = this.addLogosToBanks([data?.account], 'bank_code')[0];
    }

    if (data && data?.accounts) {
      data.accounts = this.addLogosToBanks(data?.accounts, 'bank_code');
    }

    return {
      message: 'wallet fetched successfully',
      data,
    };
  }

  @Get('transactions/:id')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.CAN_MAKE_WITHDRAWAL)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getTransaction(@Req() req: IRequest, @Param('id') transactionId: string) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.walletService.getTransaction(transactionId, req.user.store.id);

    return {
      message: 'Transaction fetched successfully',
      data,
    };
  }

  @Get('multiple')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.VIEW_WALLET)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getWallets(@Req() req: IRequest) {
    const userRole = req?.user?.role;
    const data = await this.walletService.getWalletsFromStore(req.user.store.id, userRole);

    if (data && data.wallets) {
      // Add logos to bank accounts for each wallet
      data.wallets = data.wallets.map((wallet) => {
        if (wallet.account) {
          wallet.account = this.addLogosToBanks([wallet.account], 'bank_code')[0];
        }

        if (wallet.accounts) {
          wallet.accounts = this.addLogosToBanks(wallet.accounts, 'bank_code');
        }

        return wallet;
      });
    }

    return {
      message: 'Wallets fetched successfully',
      data,
    };
  }

  @Get('balance')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.VIEW_BALANCE)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getBalance(@Req() req: IRequest) {
    const data = await this.walletService.getBalance(req.user.store.id);

    return {
      message: 'wallet fetched successfully',
      data,
    };
  }

  @Get('withdrawal-accounts')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.MANAGE_WITHDRAWAL_ACCOUNTS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getWithdrawalAccount(@Req() req: IRequest, @Query() query: { currency: CURRENCIES }) {
    const withdrawalWallets = await this.walletService.getWithdrawalAccountsFromStore(
      req.user.store.id,
      query.currency,
    );

    return {
      message: 'withdrawal accounts fetched',
      data: this.addLogosToBanks(withdrawalWallets, 'bank_code'),
    };
  }

  @Post('withdrawal-accounts')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.MANAGE_WITHDRAWAL_ACCOUNTS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async createWithdrawalAccount(@Req() req: IRequest, @Body() payload: CreateWithdrawalAccountDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const withdrawalWallet = await this.walletService.createWithdrawalAccount(req.user.store.id, payload);

    return {
      message: 'withdrawal account created',
      data: this.addLogosToBanks([withdrawalWallet], 'bank_code')[0],
    };
  }

  @Delete('withdrawal-accounts')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.MANAGE_WITHDRAWAL_ACCOUNTS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async deleteWithdrawalAccount(@Req() req: IRequest, @Body() payload: DeleteWithdrawalAccountDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const withdrawalAccount = await this.walletService.deleteWithdrawalAccountFromStore(req.user.store.id, payload.id);

    return {
      message: 'withdrawal account deleted',
      data: withdrawalAccount,
    };
  }

  @Post('withdrawal')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.CAN_MAKE_WITHDRAWAL)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async createWithdrawalRequest(@Req() req: IRequest, @Body() body: WithdrawalRequestDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.walletService.withdrawalRequest(req.user, req.user.store.id, body);

    return {
      message: 'Email sent with verification code',
      data,
    };
  }

  @Post('withdrawal/:id/resend-token')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.CAN_MAKE_WITHDRAWAL)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async resendOTPToken(@Req() req: IRequest, @Param('id') requestId: string) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.walletService.resendOTPToken(requestId, req.user.store.id);

    return {
      message: 'Email sent with verification code',
      data,
    };
  }

  @Post('withdrawal/submit')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.CAN_MAKE_WITHDRAWAL)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async submitWithdrawalRequest(@Req() req: IRequest, @Body() body: CompleteWithdrawalRequestDto) {
    paymentsEnabledGuard(req.user.store as Store);
    await this.walletService.completeWithdrawalRequest(req.user, body, req.user.store.id);

    return {
      message: 'Withdrawal request successfully made',
    };
  }

  // @Post('/test-payment')
  // @RolePermissions(SCOPES.WALLETS.VIEW_WALLET)
  // @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  // @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  // async initiateTestPayment(@Req() req: IRequest, @Body() body: CompleteWithdrawalRequestDto) {
  //   paymentsEnabledGuard(req.user.store as Store);
  //   const data = await this.walletService.createTestPayment(req.user.store.id);

  //   return {
  //     message: 'Test payment initiated successfully',
  //     data,
  //   };
  // }

  @Post('request-wallet')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.CAN_REQUEST_WALLET)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  @ApiOperation({ summary: 'Create a new wallet request' })
  async createWalletRequest(@Req() req: IRequest, @Body() body: CreateInternationalWalletRequestDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.walletService.requestWalletCreation(req.user.store.id, body);

    return {
      message: 'Wallet request submitted successfully',
      data,
    };
  }

  @Get('latest-wallet-request')
  @ApiSecurity('bearer')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard)
  async getLatestWalletRequest(@Req() req: IRequest) {
    const data = await this.walletService.getLatestWalletRequest(req.user.store.id);

    return {
      message: 'Request fetched successfully',
      data,
    };
  }

  @Get('/:id/transactions')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.WALLETS.VIEW_TRANSACTIONS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async getWalletTransactions(
    @Req() req: IRequest,
    @Query('filter') filter: any,
    @Param('id') walletId: string,
    @Query() query: PaginatedQueryDto,
  ) {
    const transactions = await this.walletService.getWalletTransactionsFromStore(
      req.user.store.id,
      walletId,
      filter,
      query,
    );

    return transactions;
  }

  @Get('get-banks')
  @ApiSecurity('bearer')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard)
  async getBanks(@Req() req: IRequest, @Query('currency') currency: CURRENCIES) {
    const banks = await this.walletService.getBanks(req.user.store.id, currency);

    if (!banks.error) {
      banks.data = this.addLogosToBanks(banks.data);
    }

    return {
      message: banks.error ? 'Error' : 'Banks gotten successfully',
      data: banks.data,
    };
  }

  @Get('admin/requests')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminGetWalletRequests(
    @Query('pagination') pagination: PaginatedQueryDto,
    @Req() req: IRequest,
    @Query('filter') filter?: any,
  ) {
    try {
      const data = await this.walletService.getWalletRequestsWithPagination(pagination, filter);

      return {
        message: 'Wallet requests fetched successfully',
        ...data,
      };
    } catch (error) {
      if (!(error instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw error;
    }
  }

  @Get('withdrawal-fees')
  @ApiSecurity('bearer')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard)
  async getWithdrawalFees(@Query('currency') currency: CURRENCIES, @Req() req: IRequest) {
    const storeUsesChowbot = req.user.store?.flags?.uses_chowbot;
    const feesToUse = storeUsesChowbot ? chowbotFees : fees;

    currency = currency ?? CURRENCIES?.NGN;

    return {
      message: 'Fees fetched successfully',
      data: {
        fees: feesToUse[currency],
        maxWithdrawalAmount: maxWithdrawalAmount[currency],
        minWithdrawalAmount: minWithdrawalAmount[currency],
      },
    };
  }

  @Get('transaction-fees')
  @ApiSecurity('bearer')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard)
  async getTransactionFees(@Query('currency') currency: CURRENCIES, @Req() req: IRequest) {
    return {
      message: 'Fees fetched successfully',
      data: {
        ...PAYMENT_FEE_PROFILES[currency],
      },
    };
  }

  @Post('resolve-account')
  @ApiSecurity('bearer')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, CountryGuard)
  async resolveAccount(@Body() data: ResolveAccountDto) {
    const account = await this.walletService.resolveAccountNumber(data);

    return {
      message: 'Account resolved',
      data: account,
    };
  }

  //payment statements
  @Post('/statements')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async createStatement(@Req() req: IRequest, @Body() body: CreatePaymentStatementDto) {
    const data = await this.walletService.createPaymentStatement(req.user.store.id, body);
    return {
      data,
      message: 'Successfully created payment statement',
    };
  }

  @Get('/statements')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getStatements(@Req() req: IRequest, @Query() query: PaginatedQueryDto) {
    const filter = (query as any).filter || {};
    filter.store = req.user.store.id;
    const data = await this.walletService.getPaginatedStatements(filter, query);
    return {
      data,
      message: 'Successfully fetched statements',
    };
  }

  @Get('/statements/:id')
  async getStatement(@Req() req: IRequest, @Param('id') id: string, @Query() query: any) {
    const data = await this.walletService.getPaymentStatement(id, query?.pin);
    return {
      data,
      message: 'Successfully fetched statement',
    };
  }

  @Get('/statements/pdf/:id')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  async getStatementsPdf(@Param('id') id: string, @Res() res: IResponse, @Query() query: any) {
    const buffer = await this.walletService.getStatementsPdfFile(id, query?.pin);
    const readable = new Readable();
    readable._read = () => {};
    readable.push(buffer);
    readable.push(null);
    readable.pipe(res);
    return {};
  }

  @Post('/add-currency-to-transactions')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async addCurrencyToTransaction() {
    const data = await this.walletService.addCurrenciesToTransactions();

    return {
      message: 'migrated transaction currencies',
      data,
    };
  }

  @Post('/add-currency-to-wallets')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async addCurrencyToWallets() {
    const data = await this.walletService.addCurrenciesToWallets();

    return {
      message: 'migrated wallet currencies',
      data,
    };
  }

  @Post('/update-recipient-code-migration')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateSpecificBanksRecipientCodes() {
    const data = await this.walletService.migrateSpecificBanksRecipientCodes();

    return {
      message: 'Recipient codes updated successfully',
      data,
    };
  }

  @Post('/migration-to-add-store-to-payment')
  @ApiOperation({ summary: 'Migrate payments linked to squad transactions' })
  @ApiQuery({
    name: 'last_processed_id',
    required: false,
    description: 'The ID of the last processed transaction. Used to continue the migration from a specific point.',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'The number of payments migrated, the ID of the last processed transaction, and the total remaining.',
  })
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migratePayments(@Query('last_processed_id') last_processed_id?: string) {
    const { migratedCount, totalCount, lastProcessedId } = await this.walletService.migratePayments(last_processed_id);

    return {
      message: `${migratedCount} payments migrated in this batch.`,
      last_processed_id: lastProcessedId,
      remaining: totalCount - migratedCount,
    };
  }

  @Get('/check-transactions')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async checkTransactions(@Query('pagination') pagination: PaginatedQueryDto) {
    const data = await this.walletResolutionsService.checkWalletTransactions(pagination);

    return { message: 'Transactions Checked', ...data };
  }

  @Post('validate-withdrawals')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async validateReferences(@Body() body: ValidateWithdrawalsDto) {
    const data = await this.walletResolutionsService.validateWithdrawals(body);
    return {
      message: 'Successfully validated withdrawals',
      data,
    };
  }

  @Post('process-withdrawal-refunds')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async processWithdrawalRefunds(@Body() body: ProcessRefundDto) {
    const data = await this.walletResolutionsService.reprocessFailedWithdrawal(body.requestId, body.reference);

    return {
      message: 'Refund processed',
      data,
    };
  }

  @Post('manually-credit-wallet')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async manuallyCreditWallet(@Body() body: ManuallyCreditWalletDto) {
    const data = await this.walletService.manuallyCreditWallet(body);

    return {
      message: 'Manually Credit Wallet',
      data,
    };
  }

  @Post('process-partial-refunds')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async processPartialRefund(@Body() body: ProcessPartialRefundDto) {
    const data = await this.walletResolutionsService.partiallyDebitFailedWithdrawal(
      body.requestId,
      body.reference,
      body.amount,
    );

    return {
      message: 'Partial Refund processed',
      data,
    };
  }

  @Post('create-squad-accounts')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async createSquadAccounts() {
    const data = await this.walletService.createSquadAccounts();

    return {
      message: 'Accounts Created',
      data,
    };
  }

  @Post('create-payaza-accounts')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async createPayazaAccounts() {
    const data = await this.walletService.createPayazaAccounts();

    return {
      message: 'Accounts Created',
      data,
    };
  }

  @Post('create-new-account')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async createAccountForSingleUser(@Body() body: { storeId: string; name?: string; businessName?: string }) {
    const data = await this.walletService.createNewAccount(body.storeId, body.businessName, body.name);

    return {
      message: 'Account Created',
      data,
    };
  }

  @Post('create-new-payaza-account')
  @ApiSecurity('bearer')
  @ApiBody({
    description: 'Details required to create a reserved Payaza account',
    schema: {
      type: 'object',
      properties: {
        storeId: { type: 'string', description: 'ID of the store' },
        name: { type: 'string', nullable: true, description: 'Optional name for the Payaza account' },
        businessName: { type: 'string', nullable: true, description: 'Optional business name for the Payaza account' },
      },
      required: ['storeId'], // Specify required properties
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
  })
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async createReservedPayazaAccount(@Body() body: { storeId: string; name?: string; businessName?: string }) {
    const data = await this.walletService.createReservedPayazaAccount(body.storeId, body.businessName, body.name);

    return {
      message: 'Account Created',
      data,
    };
  }

  // @Post('resend-failed-account-emails')
  // @ApiSecurity('bearer')
  // @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  // async resendFailedAccountEmails() {
  //   const data = await this.walletService.resendBusinessEmailCreatedEmails();

  //   return {
  //     message: 'Resend Triggered',
  //     data,
  //   };
  // }

  @Post('trigger-missing-squad-webhooks')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async triggerMissingSquadWebhooks(@Body() body: TriggerMissingSquadWebhooksDto) {
    const data = await this.walletResolutionsService.triggerMissingSquadWebhooks(body);

    return {
      message: 'Webhooks triggered',
      data,
    };
  }

  @Post('migrate-payment-ids')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migratePaymentIds() {
    const data = await this.walletService.migratePaymentIds();

    return {
      message: 'Migration Processed',
      data,
    };
  }

  @Post('trigger-failed-squad-webhooks')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async triggerFailedSquadWebhooks() {
    const data = await this.walletResolutionsService.triggerFailedSquadWebhooks();

    return {
      message: 'Webhooks triggered',
      data,
    };
  }

  // @Post('test-race-condition')
  // @ApiSecurity('bearer')
  // @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  // async testRaceCondition(@Body() body: { accountNumber: string; amountOne: number; amountTwo: number }) {
  //   const data = await this.walletService.testRaceCondition(body);

  //   return {
  //     message: 'Triggered',
  //     data,
  //   };
  // }

  @Post('update-has-completed-kyc')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async updateHasCompletedKyc() {
    const data = await this.walletService.updateHasCompletedKyc();

    return {
      message: 'Updated',
      data,
    };
  }

  @Get('public/withdrawal-request/:id')
  async getWithdrawalRequest(@Param('id') id: string) {
    const data = await this.walletService.getPublicWithdrawalRequest(id);

    return {
      message: 'Withdrawal request fetched successfully',
      data,
    };
  }

  @Get('/withdrawal-request/:id/pdf')
  async getPdf(@Param('id') id: string, @Res() res: IResponse) {
    const buffer = await this.walletService.generateWithdrawalRequestPdf(id);
    const readable = new Readable();
    readable._read = () => {};
    readable.push(buffer);
    readable.push(null);
    readable.pipe(res);
    return {};
  }

  @Post('admin/approve-or-reject-wallet-request')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Approve or reject a wallet request' })
  async updateWalletRequest(@Body() body: UpdateWalletRequestDto) {
    const data = await this.walletService.updateWalletRequest(body);

    return {
      message: `Wallet request ${body.status.toLowerCase()} successfully`,
      data,
    };
  }

  @Post('admin/requests/:id/approve')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Approve a wallet request' })
  async approveWalletRequest(@Param('id') id: string) {
    const data = await this.walletService.updateWalletRequest({
      wallet_request_id: id,
      status: WALLET_REQUEST_STATUS.APPROVED,
    });

    return {
      message: 'Wallet request approved successfully',
      data,
    };
  }

  @Post('admin/requests/:id/reject')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Reject a wallet request' })
  async rejectWalletRequest(@Param('id') id: string, @Body() body: { message: string }) {
    const data = await this.walletService.updateWalletRequest({
      wallet_request_id: id,
      status: WALLET_REQUEST_STATUS.REJECTED,
      admin_notes: body.message,
    });

    return {
      message: 'Wallet request rejected successfully',
      data,
    };
  }

  @Get('admin/withdrawal-requests/:id')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Get withdrawal request details' })
  async getWithdrawalRequestDetails(@Param('id') id: string) {
    const data = await this.walletService.getWithdrawalRequestDetails(id);

    return {
      message: 'Withdrawal request details fetched successfully',
      data,
    };
  }

  @Post('admin/withdrawal-requests/:id/mark-successful')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Mark a withdrawal request as successful' })
  async markWithdrawalSuccessful(@Param('id') id: string) {
    const data = await this.walletService.markWithdrawalSuccessful(id);

    return {
      message: 'Withdrawal request marked as successful',
      data,
    };
  }

  @Post('admin/withdrawal-requests/:id/mark-failed')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Mark a withdrawal request as failed with internal failure' })
  async markWithdrawalFailed(@Param('id') id: string) {
    const data = await this.walletService.markWithdrawalFailed(id);

    return {
      message: 'Withdrawal request marked as failed with internal failure',
      data,
    };
  }

  @Post('sync-bank-accounts-to-customer-io')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async syncBankAccountsToCustomerIo() {
    const results = await this.walletService.syncBankAccountsToCustomerIo();

    return {
      message: 'Bank accounts synced to Customer.io',
      data: results,
    };
  }

  @Post('recalculate-balance/:walletId')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Recalculate wallet balance by processing all transactions' })
  @ApiParam({ name: 'walletId', description: 'ID of the wallet to recalculate balance for' })
  async recalculateWalletBalance(@Param('walletId') walletId: string) {
    const data = await this.walletService.recalculateWalletBalance(walletId);

    return {
      message: 'Wallet balance recalculated successfully',
      data,
    };
  }

  @Post('trigger-wallet-payment-email')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async triggerWalletPaymentEmail(@Body() body: { name: string; amount: string; email: string; code: string }) {
    const data = await this.walletService.triggerWalletPaymentEmail(body);

    return {
      message: 'Wallet payment email triggered successfully',
      data,
    };
  }

  addLogosToBanks(banks: any[], code: string = 'code') {
    const bankLogos = logos;

    const banksWithLogos = banks.map((b) => ({
      ...b,
      image: bankLogos[b[code]] ?? 'https://res.cloudinary.com/catlog/image/upload/v1667571113/bank-logos/default.svg',
    }));

    return banksWithLogos;
  }
}
