import { Injectable } from '@nestjs/common';

/**
 * Decorator that marks a service for automatic registration in the DirectServiceAccessService.
 * This is a convenience decorator that can be used instead of manual registration.
 *
 * Usage:
 * @RegisterService('MyService')
 * @Injectable()
 * export class MyService {
 *   // service implementation
 * }
 *
 * @param serviceName - The name to register the service under
 */
export function RegisterService(serviceName?: string) {
  return function <T extends { new (...args: any[]): any }>(constructor: T) {
    // Apply the Injectable decorator
    Injectable()(constructor);

    // Store the service name on the constructor for later registration
    const name = serviceName || constructor.name;
    (constructor as any).__serviceName = name;

    return constructor;
  };
}

/**
 * Helper function to get the service name from a constructor that was decorated with @RegisterService
 */
export function getServiceName(constructor: any): string | undefined {
  return constructor.__serviceName;
}
