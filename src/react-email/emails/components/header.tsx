import { Row, Column, Img, Heading } from '@react-email/components';
import * as React from 'react';

interface Props {
  type: keyof typeof headerData;
  children?: React.ReactNode;
  isLight?: boolean;
}
const EmailHeader: React.FC<Props> = ({ type, children, isLight }) => {
  return (
    <Row>
      <Column
        style={{
          background: headerData[type]?.background,
          backgroundSize: 'cover',
          backgroundPosition: '30% 100%',
          backgroundRepeat: 'no-repeat',
        }}
        className="py-[20px] sm:py-[50px] px-[7%] sm:px-[8%]"
      >
        <Img
          src={
            isLight
              ? 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/logo-lockup-white.png'
              : 'https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/logo-lockup-purple.png'
          }
          className="w-20 object-cover"
          alt="Catlog Logo"
        />
        <Heading
          as="h1"
          className={`${isLight ? 'text-white ' : 'text-primary-500 '} text-3lg sm:text-[42px] mt-[50px] font-semibold`}
        >
          {children || headerData[type]?.header}
        </Heading>
      </Column>
    </Row>
  );
};
export default EmailHeader;

const headerData = {
  product_export_successful: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1715457685/email-images/import.png')",
    header: (
      <>
        Products exported
        <br /> Successfully
      </>
    ),
  },
  withdrawal_failed: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1680484941/email-images/reversed-funds-header.png')",
    header: (
      <>
        We reversed
        <br /> some Funds
      </>
    ),
  },
  kyc_rejected: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/subscription-cancelled-header.png')",
    header: (
      <>
        Your payments
        <br /> approval failed
      </>
    ),
  },
  user_referred: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1699381073/email-images/referral-cover.png')",
    header: (
      <>
        You referred a
        <br /> friend to Catlog
      </>
    ),
  },
  funds_reversed: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1680484941/email-images/reversed-funds-header.png')",
    header: (
      <>
        We reversed
        <br /> some funds
      </>
    ),
  },
  delivery_booked: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1694363667/email-images/deliveries-header.png')",
    header: (
      <>
        Your Delievery
        <br /> has been booked
      </>
    ),
  },
  credits_received: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/withdrawal-successful-header.png')",
    header: (
      <>
        You’ve received <br />
        some Credits.
      </>
    ),
  },
  sales_milestone: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/withdrawal-successful-header.png')",
    header: <></>,
  },
  tokens_threshold_notification: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1715457709/email-images/token-balance.png')",
    header: (
      <>
        Your token <br /> balance is low
      </>
    ),
  },
  orders_milestone: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1689199459/email-images/orders-header.png')",
    header: <></>,
  },
  store_visits_milestone: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1689198906/email-images/store-visits-header.png')",
    header: <></>,
  },
  seller_delivery_status: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1694363667/email-images/deliveries-header.png')",
    header: <></>,
  },
  welcome_email: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/welcome-header.png')",
    header: <></>,
  },
  accept_invite: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/light-purple-header.png')",
    header: (
      <>
        Someone sent <br /> you an invite
      </>
    ),
  },
  payment_received: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/money-in-header.png')",
    header: (
      <>
        You've received
        <br /> some money
      </>
    ),
  },
  extra_token_purchase: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/money-in-header.png')",
    header: (
      <>
        You bought some
        <br /> Chowbot Tokens
      </>
    ),
  },
  withdrawal_successful: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/withdrawal-successful-header.png')",
    header: (
      <>
        Your withdrawal
        <br /> was successful
      </>
    ),
  },
  order_status: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/order-header.png')",
    header: <></>,
  },
  verify_bvn: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/confirmation-code-header.png')",
    header: (
      <>
        Here's a code to
        <br /> verify your BVN
      </>
    ),
  },
  verify_withdrawal: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/confirmation-code-header.png')",
    header: (
      <>
        You requested
        <br /> a withdrawal
      </>
    ),
  },
  verify_wallet_payment: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/confirmation-code-header.png')",
    header: (
      <>
        You requested
        <br /> a wallet payment
      </>
    ),
  },
  reset_password: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/reset-password-header.png')",
    header: (
      <>
        Here's a code to
        <br /> set your password
      </>
    ),
  },
  payment_success: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/payment-successful-header.png')",
    header: (
      <>
        Your payment
        <br /> was successful
      </>
    ),
  },
  invoice_received: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/invoice-email-header.png')",
    header: (
      <>
        Someone sent
        <br /> you an invoice
      </>
    ),
  },
  payments_activated: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/payments-approved.png')",
    header: (
      <>
        You can now
        <br /> collect payments
      </>
    ),
  },
  verify_email: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/confirm-email-header.png')",
    header: (
      <>
        Here's a code to
        <br /> verify your email
      </>
    ),
  },
  new_subscription: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/successful-subscription-header.png')",
    header: (
      <>
        Your Subscription
        <br /> was successful
      </>
    ),
  },
  subscription_successful: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/successful-subscription-header.png')",
    header: (
      <>
        Your Subscription
        <br /> was renewed
      </>
    ),
  },
  renew_subscription: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/renew-subscription-header.png')",
    header: (
      <>
        Renew your
        <br /> subscription
      </>
    ),
  },
  subscription_reminder: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/renew-subscription-header.png')",
    header: (
      <>
        Your Catlog
        <br /> Subscription
      </>
    ),
  },
  trial_ended: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/renew-subscription-header.png')",
    header: (
      <>
        Your Free
        <br /> Trial Ended
      </>
    ),
  },
  subscription_cancelled: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/subscription-cancelled-header.png')",
    header: (
      <>
        Your subscription
        <br /> was cancelled
      </>
    ),
  },
  order_received: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/order-header.png')",
    header: (
      <>
        You have an order
        <br /> from your store
      </>
    ),
  },
  request_approved: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1735859160/email-images/wallet-request-approved-header.png')",
    header: (
      <>
        Your Wallet Request
        <br /> has been Approved!
      </>
    ),
  },
  conversion_successful: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1735857632/email-images/conversion-successful-header.png')",
    header: (
      <>
        Your Conversion
        <br /> was successful!
      </>
    ),
  },
  weekly_monthly_summaries: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1739441649/email-images/Store_ilus_1.png')",
    header: <></>,
  },
  affiliate_invite: {
    background:
      "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/light-purple-header.png')",
    header: (
      <>
        You're Now
        <br /> an Affiliate
      </>
    ),
  },
  affiliate_order: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1669485894/email-images/order-header.png')",
    header: (
      <>
        New Order
        <br /> from your Link
      </>
    ),
  },
  stock_threshold_notification: {
    background: "url('https://res.cloudinary.com/catlog/image/upload/v1715457685/email-images/import.png')",
    header: (
      <>
        Low Item
        <br /> Stock Alert!
      </>
    ),
  },
};
