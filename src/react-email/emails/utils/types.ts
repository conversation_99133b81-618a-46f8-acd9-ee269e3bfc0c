export enum EmailMetricKey {
  TOTAL_ORDERS = 'total_orders',
  PAYMENT = 'payments',
  TOTAL_STORE_VISITS = 'store_visits',
  NEW_CUSTOMERS_COUNT = 'new_customers',
  NGN = 'NGN',
  GHS = 'GHS',
  USD = 'USD',
  GBP = 'GBP',
  KES = 'KES',
  ZAR = 'ZAR',
  CAD = 'CAD',
}

export type EmailMetric = {
  value: number | string;
  change: number;
  key: EmailMetricKey;
};

export interface EmailSummaryData {
  preview_text: string;

  name: string;
  store_name: string;
  summary_type: string;

  store_performance: EmailMetric[];
  payments_summary: EmailMetric[];

  top_customers: {
    name: string;
    orders: number;
  }[];

  top_products: {
    name: string;
    orders: number;
  }[];
}

export interface EmailOrderData {
  preview_text: string;
  store_name: string;
  name: string;
  store_color?: string;
  store_logo: string;
  store_phone?: string;
  is_affiliate?: boolean;
  pending_payment?: boolean;
  order: {
    order_id: string;
    link: string;
    payment_link: string;
    order_status: string;
    products: {
      name: string;
      price: number | string;
      quantity: number;
      image: string;
    }[];
    currency: string;
    total: string | number;
    delivery_method: string;
    delivery_area?: string;
    delivery_address?: string;
    fees: {
      label: string;
      amount: number;
    }[];
    customer_info?: {
      name: string;
      email?: string;
      phone?: string;
    };
  };
}

export interface AffiliateInviteProps {
  name: string;
  store_name: string;
  preview_text: string;
  invite_link: string;
  store_phone: string;
}
