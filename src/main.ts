import 'dotenv/config';
// if (process.env.NODE_ENV === 'production') {
//   require('@opentelemetry/auto-instrumentations-node/register');
// }
import * as Sentry from '@sentry/node';
import '@sentry/tracing';

import { NestFactory } from '@nestjs/core';
import { corsConfiguration } from './config/cors-configuration';
import { APIToolkit } from 'apitoolkit-express';
import { AppModule } from './app.module';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe, HttpException, HttpStatus } from '@nestjs/common';
import { MicroserviceOptions, NatsOptions } from '@nestjs/microservices';
import { OutboundMessageSerializer } from './serializers/outbound-message-serializer';
import { InboundMessageDeserializer } from './serializers/inbound-message-deserializer';
import { LowercasePipe } from './pipe/lowercase.pipe';
import { utilities } from 'nest-winston';
import winston from 'winston';
import mongoose from 'mongoose';
import { SnakecaseInterceptor } from './interceptors/snakecase.interceptor';
import { CatlogLogger } from './utils/logger.util';
import { RavenInterceptor } from 'nest-raven';
import { json as bodyParserJson } from 'body-parser';
const helmet = require('helmet'); // Importing helmet using require

const PRODUCTION_ENV = 'production';
const PORT = process.env.PORT || 4000;

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  tracesSampleRate: 1.0,
  environment: process.env.NODE_ENV,
  debug: true,
  enabled: true,
  beforeSend(event, hint) {
    const originalException = hint && (hint as any).originalException;

    if (originalException instanceof HttpException) {
      const statusCode = originalException.getStatus();
      if (statusCode === HttpStatus.NOT_FOUND) {
        return null;
      }
    }

    const message = (originalException && (originalException as any).message) || (event as any)?.message;

    if (message && (/not found/i.test(String(message)) || message.includes('not found'))) {
      return null;
    }

    if (message && message.includes('to debit wallets')) {
      return null;
    }

    return event;
  },
});

async function bootstrap() {
  let cors: boolean | CorsOptions;

  if (process.env.NODE_ENV !== PRODUCTION_ENV) {
    mongoose.set('debug', true);
    cors = true;
  } else {
    cors = corsConfiguration;
  }

  const app = await NestFactory.create(AppModule, {
    cors,
    logger: new CatlogLogger({
      winstonOptions: {
        format: winston.format.combine(
          winston.format.json(),
          winston.format.prettyPrint(),
          utilities.format.nestLike(process.env.npm_package_name, {
            prettyPrint: true,
          }),
        ),
        transports: [new winston.transports.Console()],
      },
    }),
  });

  // Use Helmet middleware
  app.use(helmet());

  // Customize Helmet for additional headers
  app.use(
    helmet.contentSecurityPolicy({
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'"],
        styleSrc: ["'self'"],
        imgSrc: ["'self'", 'data:'],
        fontSrc: ["'self'"],
      },
    }),
  );
  app.use(helmet.referrerPolicy({ policy: 'no-referrer' }));
  app.use(helmet.permittedCrossDomainPolicies());
  app.use(helmet.hsts({ maxAge: 63072000, includeSubDomains: true, preload: true }));
  app.use(helmet.frameguard({ action: 'deny' }));
  app.use(helmet.noSniff());

  // Access the underlying Express application
  const expressApp = app.getHttpAdapter().getInstance();
  expressApp.set('trust proxy', true);

  app.use(
    bodyParserJson({
      limit: '50mb',
      verify(req, _, buf, encoding) {
        req.headers['bodyRaw'] = Buffer.from(buf).toString(encoding as any);
      },
    }),
  );

  // app.use(
  //   APIToolkit.middleware({
  //     serviceName: 'Catlog',
  //     redactHeaders: ['authorization', 'cookie'],
  //     redactResponseBody: ['$.creditCardNumber'], // jsonpath to redact credit card number from response body
  //     redactRequestBody: ['$.password'], // jsonpath to redact password from request body
  //     captureRequestBody: true, // capture request body and send it to your apitoolkit dashboard
  //     captureResponseBody: true, // capture response body and send it to your apitoolkit dashboard
  //   }),
  // );

  if (process.env.NODE_ENV !== PRODUCTION_ENV) {
    const options = new DocumentBuilder()
      .setTitle('Catlog API')
      .setDescription('API Documentation for the Catlog API')
      .setVersion('1.0')
      .addBearerAuth({ type: 'http' })
      .build();

    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup('api-docs', app, document);
  }
  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  app.useGlobalPipes(new LowercasePipe(['email']));

  app.useGlobalInterceptors(new SnakecaseInterceptor());
  app.useGlobalInterceptors(new RavenInterceptor());

  app.enableShutdownHooks();

  // comes after all other route handlers
  // app.use(APIToolkit.errorMiddleware());

  await app.listen(PORT, () => console.log(`Server listening on ${PORT}`));

  const microServiceConfig = app.get('ConfigService').internalConfig.brokerTransportConfiguration as NatsOptions;

  const microservice = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: microServiceConfig.transport,
    options: {
      ...microServiceConfig.options,
      serializer: new OutboundMessageSerializer(),
      deserializer: new InboundMessageDeserializer(),
    },
  });
  microservice.enableShutdownHooks();
  microservice.useGlobalPipes(new ValidationPipe());
  microservice.listen(() => {
    console.log('microservices started');
  });
}

bootstrap();
