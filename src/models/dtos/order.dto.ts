import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsDateString,
  IsEnum,
  isEnum,
  IsOptional,
  IsString,
  ArrayNotEmpty,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { DELIVERY_METHODS, ORDER_CHANNELS, ORDER_STATUSES } from '../../modules/orders/order.schema';
import { CUSTOMER_ORDERS_TAG, ORDER_PAID_TAG } from '../../enums/order.enum';
import { Type } from 'class-transformer';

export class OrdersFilterQueryDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  search?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  store?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  to?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ORDER_STATUSES)
  status?: ORDER_STATUSES;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ORDER_PAID_TAG)
  payment_status?: ORDER_PAID_TAG;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  customer?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(DELIVERY_METHODS)
  fulfillment_method?: DELIVERY_METHODS;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ORDER_CHANNELS)
  channel?: ORDER_CHANNELS;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  products?: string[];
}

export class CustomersFilterQueryDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  search?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  store?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(CUSTOMER_ORDERS_TAG)
  status?: CUSTOMER_ORDERS_TAG;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  to?: string;
}

export class BulkOrderUpdateItemDto {
  @ApiProperty({ description: 'Order ID to update' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    enum: ORDER_STATUSES,
    description: 'New status for the order. Cannot update orders that are FULFILLED or CANCELLED',
    example: ORDER_STATUSES.PROCESSING,
  })
  @IsEnum(ORDER_STATUSES)
  @IsNotEmpty()
  status: ORDER_STATUSES;

  @ApiProperty({
    description: 'Optional reason for the status change',
    required: false,
  })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class BulkOrderUpdateDto {
  @ApiProperty({
    type: [BulkOrderUpdateItemDto],
    description: 'Array of orders to update with their new statuses',
    example: [
      { id: '507f1f77bcf86cd799439011', status: 'PROCESSING' },
      { id: '507f1f77bcf86cd799439012', status: 'PAYMENT_RECEIVED' },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => BulkOrderUpdateItemDto)
  orders: BulkOrderUpdateItemDto[];
}

export class BulkOrderUpdateResponseDto {
  @ApiProperty({ description: 'Number of orders successfully updated' })
  updated_count: number;

  @ApiProperty({ description: 'Number of orders that failed to update' })
  failed_count: number;

  @ApiProperty({
    type: [String],
    description: 'IDs of orders that were successfully updated',
  })
  updated_orders: string[];

  @ApiProperty({
    type: [Object],
    description: 'Details of orders that failed to update with reasons',
  })
  failed_orders: Array<{
    id: string;
    reason: string;
    current_status?: ORDER_STATUSES;
  }>;

  @ApiProperty({ description: 'Overall operation message' })
  message: string;
}

export class BulkMarkAsPaidDto {
  @ApiProperty({
    type: [String],
    description: 'Array of order IDs to mark as paid',
    example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  order_ids: string[];
}

export class BulkMarkAsPaidResponseDto {
  @ApiProperty({ description: 'Number of orders successfully marked as paid' })
  updated_count: number;

  @ApiProperty({ description: 'Number of orders that failed to be marked as paid' })
  failed_count: number;

  @ApiProperty({
    type: [String],
    description: 'IDs of orders that were successfully marked as paid',
  })
  updated_orders: string[];

  @ApiProperty({
    type: [Object],
    description: 'Details of orders that failed to be marked as paid with reasons',
  })
  failed_orders: Array<{
    id: string;
    reason: string;
    current_status?: ORDER_STATUSES;
    is_paid?: boolean;
  }>;

  @ApiProperty({ description: 'Overall operation message' })
  message: string;
}
