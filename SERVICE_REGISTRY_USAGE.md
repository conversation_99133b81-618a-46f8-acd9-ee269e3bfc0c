# Type-Safe Service Registry Usage Guide

This guide demonstrates how to use the centralized `DirectServiceAccessService` to access services throughout your application with full type safety without needing to create separate interfaces.

## Availability

The `DirectServiceAccessService` is available throughout the application via the `SharedModule`, which most modules already import. You don't need to import `DirectServiceAccessModule` directly in your feature modules.

## Registering Services

Each service should register itself with the service registry in its constructor:

```typescript
// In your service class constructor
constructor(
  // other dependencies...
  private readonly directService: DirectServiceAccessService,
) {
  // Register this service in the registry
  this.directService.register('MyServiceName', this);
}
```

## Accessing Services

### Basic Usage

You can access any registered service using the convenience methods:

```typescript
// In any service or controller that needs to use another service
constructor(
  private readonly directService: DirectServiceAccessService,
) {}

async someMethod() {
  // Access the OrdersService
  const order = await this.directService.orders().findOne(orderId, storeId);

  // Access the StoreService
  const store = await this.directService.stores().getStore({ _id: storeId });

  // Access the UserService
  const user = await this.directService.users().findOne(userId);
}
```

### With Type Safety

You can specify the exact type when accessing a service for better type checking:

```typescript
import { OrdersService } from './modules/orders/orders.service';
import { StoreService } from './modules/store/services/store.service';

// Explicitly specify the service type
const ordersService = this.directService.orders<OrdersService>();
const order = await ordersService.findOne(orderId, storeId);

// For services that aren't in the convenience methods
const myCustomService = this.directService.service<MyCustomService>('MyCustomService');
await myCustomService.doSomething();
```

### Type Inference

TypeScript can often infer the correct types based on usage:

```typescript
async getOrderDetails(orderId: string) {
  // TypeScript infers that orders() returns something with a findOne method
  const order = await this.directService.orders().findOne(orderId);

  // TypeScript infers that stores() returns something with a getStore method
  const store = await this.directService.stores().getStore({ _id: order.store });

  return { order, store };
}
```

## Advanced Usage

### Accessing Multiple Services

```typescript
async processOrderPayment(orderId: string, paymentData: any) {
  // Get the order
  const order = await this.directService.orders().findOne(orderId);

  // Create a payment using the PaymentService
  const payment = await this.directService.payments().createPayment({
    amount: order.total_amount,
    currency: order.currency,
    orderId: order._id,
    ...paymentData
  });

  // Update the order with the payment information
  await this.directService.orders().updateOrder(
    { _id: orderId },
    { payment_id: payment._id, payment_status: 'paid' }
  );

  // Send notification using UserService
  await this.directService.users().sendNotification(order.user, {
    title: 'Payment Successful',
    body: `Your payment of ${order.total_amount} was successful.`
  });

  return { order, payment };
}
```

### Dynamic Service Access

```typescript
async getServiceByName(serviceName: string, methodName: string, ...args: any[]) {
  const service = this.directService.service<any>(serviceName);

  if (service && typeof service[methodName] === 'function') {
    return await service[methodName](...args);
  }

  throw new Error(`Service ${serviceName} or method ${methodName} not found`);
}
```

## Benefits of This Approach

1. **Type Safety**: Full TypeScript type checking without manual interface maintenance
2. **Less Boilerplate**: No need to create and maintain separate interface files
3. **Flexibility**: Works with any service regardless of its structure
4. **Direct Access**: Call methods directly without going through the broker
5. **Centralized Management**: All service access is managed through one centralized service
6. **Simplified Imports**: Available through SharedModule with no additional imports needed

## Migrating from Broker Pattern

When migrating from the broker pattern, you can gradually replace broker calls:

```typescript
// Old broker approach
const result = await this.brokerTransport.send<ReturnType>(BROKER_PATTERNS.SERVICE.METHOD, params).toPromise();

// New direct service approach
const result = await this.directService.service<ServiceType>('ServiceName').method(params);
```

For common services, use the convenience methods:

```typescript
// Old broker approach
const store = await this.brokerTransport
  .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
  .toPromise();

// New direct service approach
const store = await this.directService.stores().getStore({ _id: storeId });
```

This approach provides a clean, type-safe way to communicate between services without the overhead of maintaining separate interface files.
